# 📊 GRAFANA INTEGRATION PLAN - PLATFORM RUST

## 🎯 **<PERSON><PERSON><PERSON> tiêu chính**
- **Application Performance Monitoring (APM)**
- **Infrastructure Monitoring** 
- **Business Metrics Tracking**
- **Real-time Alerting**

---

## **📋 TỔNG QUAN HỆ THỐNG HIỆN TẠI**

### **Tech Stack:**
- **Backend:** Rust Axum Framework
- **Database:** PostgreSQL với SQLx
- **Authentication:** JWT với RBAC system
- **Container:** Docker với docker-compose
- **Logging:** tracing-subscriber
- **Documentation:** OpenAPI/Swagger

### **Architecture hiện tại:**
```
Client → Axum Router → Middleware (Auth/RBAC) → Handlers → Services → Repository → PostgreSQL
```

---

## **🚀 IMPLEMENTATION PHASES**

## **PHASE 1: METRICS COLLECTION INFRASTRUCTURE**

### **1.1 Dependencies Setup**
```toml
# Thêm vào Cargo.toml
metrics = "0.23"
metrics-exporter-prometheus = "0.15" 
axum-prometheus = "0.7"
tower-http = { version = "0.6", features = ["cors", "fs", "metrics"] }
```

### **1.2 Metrics Middleware**
**Tạo file:** `src/routes/middleware/metrics.rs`

```rust
use axum::{extract::MatchedPath, http::Request, middleware::Next, response::Response};
use metrics::{counter, histogram, gauge};
use std::time::Instant;

pub async fn metrics_middleware<B>(
    MatchedPath(path): MatchedPath,
    request: Request<B>,
    next: Next<B>,
) -> Response {
    let start = Instant::now();
    let method = request.method().clone();
    
    let response = next.run(request).await;
    
    let duration = start.elapsed();
    let status = response.status().as_u16();
    
    // HTTP Request metrics
    counter!("http_requests_total", 
        "method" => method.to_string(), 
        "path" => path.clone(), 
        "status" => status.to_string()
    ).increment(1);
    
    histogram!("http_request_duration_seconds", 
        "method" => method.to_string(), 
        "path" => path
    ).record(duration.as_secs_f64());
    
    response
}

// Database metrics helpers
pub fn record_db_connection_acquired() {
    counter!("database_connections_acquired_total").increment(1);
}

pub fn record_db_query_duration(operation: &str, duration: std::time::Duration) {
    histogram!("database_query_duration_seconds", "operation" => operation)
        .record(duration.as_secs_f64());
}

pub fn set_db_active_connections(count: u32) {
    gauge!("database_active_connections").set(count as f64);
}

// Authentication metrics
pub fn record_auth_attempt(success: bool, method: &str) {
    counter!("auth_attempts_total", 
        "success" => success.to_string(), 
        "method" => method
    ).increment(1);
}

// Business metrics
pub fn record_user_registration() {
    counter!("user_registrations_total").increment(1);
}

pub fn record_permission_check(permission: &str, granted: bool) {
    counter!("permission_checks_total", 
        "permission" => permission, 
        "granted" => granted.to_string()
    ).increment(1);
}
```

### **1.3 Metrics Export Setup**
**Update src/main.rs:**

```rust
use metrics_exporter_prometheus::{Matcher, PrometheusBuilder, PrometheusHandle};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // ... existing setup ...

    // Prometheus metrics setup
    let prometheus_handle = setup_metrics();

    // ... existing router setup ...

    // Add metrics endpoint
    app = app.route("/metrics", axum::routing::get(move || async move {
        prometheus_handle.render()
    }));

    // ... rest of main ...
}

fn setup_metrics() -> PrometheusHandle {
    PrometheusBuilder::new()
        .with_http_listener(([0, 0, 0, 0], 9090))
        .install()
        .expect("Failed to install Prometheus recorder")
}
```

---

## **PHASE 2: LOGGING INFRASTRUCTURE**

### **2.1 Enhanced Logging Dependencies**
```toml
# Update Cargo.toml
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "fmt"] }
tracing-appender = "0.2"
serde_json = "1.0"
uuid = { version = "1.11", features = ["v4", "serde"] }
```

### **2.2 Structured Logging Setup**
**Tạo file:** `src/utils/logging.rs`

```rust
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use std::io;

pub fn init_logging() -> anyhow::Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| "platform_rust=info,tower_http=info".into());

    // Console layer
    let console_layer = tracing_subscriber::fmt::layer()
        .with_target(false)
        .with_ansi(true);

    // File layer with JSON format
    let file_appender = RollingFileAppender::new(Rotation::DAILY, "logs", "platform-rust.log");
    let file_layer = tracing_subscriber::fmt::layer()
        .with_writer(file_appender)
        .json()
        .with_current_span(false)
        .with_span_list(true);

    tracing_subscriber::registry()
        .with(env_filter)
        .with(console_layer)
        .with(file_layer)
        .init();

    Ok(())
}

// Request tracing with correlation ID
use axum::{extract::Request, middleware::Next, response::Response};
use uuid::Uuid;

pub async fn request_tracing_middleware<B>(
    mut request: Request<B>,
    next: Next<B>,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();
    
    // Add correlation ID to request headers
    request.headers_mut().insert(
        "x-correlation-id",
        correlation_id.parse().unwrap(),
    );

    let span = tracing::info_span!(
        "http_request",
        method = %request.method(),
        path = %request.uri().path(),
        correlation_id = %correlation_id
    );

    async move {
        let response = next.run(request).await;
        tracing::info!(
            status = %response.status(),
            "Request completed"
        );
        response
    }.instrument(span).await
}
```

---

## **PHASE 3: INFRASTRUCTURE SETUP**

### **3.1 Docker Compose Enhancement**
**Update docker-compose.yml:**

```yaml
services:
  platform-rust:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: "1"
    container_name: platform-rust-app
    ports:
      - "8386:8386"
    environment:
      - DATABASE_URL=/app/data/database.db
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8386
      - JWT_SECRET=${JWT_SECRET:-super-secret-jwt-key-for-development}
      - RUST_LOG=${RUST_LOG:-platform_rust=debug,tower_http=debug}
      - DB_MAX_CONNECTIONS=${DB_MAX_CONNECTIONS:-16}
      - DB_MIN_IDLE_CONNECTIONS=${DB_MIN_IDLE_CONNECTIONS:-4}
      - DB_CONNECTION_TIMEOUT_SECS=${DB_CONNECTION_TIMEOUT_SECS:-10}
    volumes:
      - app_data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8386/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - monitoring

  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alerts:/etc/prometheus/alerts
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--rule=/etc/prometheus/alerts/*.yml'
    restart: unless-stopped
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:10.1.0
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_AUTH_ANONYMOUS_ENABLED=false
    restart: unless-stopped
    networks:
      - monitoring

  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - loki_data:/loki
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped
    networks:
      - monitoring

  promtail:
    image: grafana/promtail:2.9.0
    container_name: promtail
    volumes:
      - ./logs:/var/log/platform-rust
      - ./monitoring/promtail.yml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    restart: unless-stopped
    networks:
      - monitoring
    depends_on:
      - loki

volumes:
  app_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local

networks:
  monitoring:
    driver: bridge
```

### **3.2 Prometheus Configuration**
**Tạo file:** `monitoring/prometheus.yml`

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets: []

scrape_configs:
  - job_name: 'platform-rust'
    static_configs:
      - targets: ['platform-rust:8386']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

### **3.3 Grafana Provisioning**
**Tạo file:** `monitoring/grafana/provisioning/datasources/datasources.yml`

```yaml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
```

**Tạo file:** `monitoring/grafana/provisioning/dashboards/dashboard.yml`

```yaml
apiVersion: 1

providers:
  - name: 'Platform Rust Dashboards'
    orgId: 1
    folder: 'Platform Rust'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
```

### **3.4 Loki Configuration**
**Tạo file:** `monitoring/loki.yml`

```yaml
auth_enabled: false

server:
  http_listen_port: 3100

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

storage_config:
  boltdb_shipper:
    active_index_directory: /loki/boltdb-shipper-active
    cache_location: /loki/boltdb-shipper-cache
    shared_store: filesystem
  filesystem:
    directory: /loki/chunks

limits_config:
  retention_period: 744h # 30 days
  ingestion_rate_mb: 16
  ingestion_burst_size_mb: 32

chunk_store_config:
  max_look_back_period: 0s

table_manager:
  retention_deletes_enabled: true
  retention_period: 744h
```

### **3.5 Promtail Configuration**
**Tạo file:** `monitoring/promtail.yml`

```yaml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: platform-rust-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: platform-rust
          __path__: /var/log/platform-rust/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            timestamp: timestamp
            message: message
            correlation_id: correlation_id
            module: module_path
      - labels:
          level:
          correlation_id:
          module:
      - timestamp:
          source: timestamp
          format: RFC3339
```

---

## **PHASE 4: DASHBOARD DEVELOPMENT**

### **4.1 API Overview Dashboard**
**File:** `monitoring/grafana/dashboards/api-overview.json`

**Key Panels:**
1. **Request Rate:** `rate(http_requests_total[5m])`
2. **Response Time P99:** `histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))`
3. **Error Rate:** `rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])`
4. **Top Endpoints:** `topk(10, sum by(path) (rate(http_requests_total[5m])))`
5. **Status Code Distribution:** `sum by(status) (rate(http_requests_total[5m]))`

### **4.2 Authentication Dashboard**
**Key Panels:**
1. **Login Success Rate:** `rate(auth_attempts_total{success="true"}[5m])`
2. **Failed Auth by Method:** `sum by(method) (rate(auth_attempts_total{success="false"}[5m]))`
3. **JWT Validation Errors:** `rate(jwt_validation_errors_total[5m])`
4. **Active Sessions:** `active_user_sessions`

### **4.3 Database Dashboard**
**Key Panels:**
1. **Connection Pool Usage:** `database_active_connections / database_max_connections`
2. **Query Duration:** `rate(database_query_duration_seconds_sum[5m]) / rate(database_query_duration_seconds_count[5m])`
3. **Slow Queries:** `histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m]))`
4. **Connection Errors:** `rate(database_connection_errors_total[5m])`

### **4.4 Business Metrics Dashboard**
**Key Panels:**
1. **User Registrations:** `increase(user_registrations_total[1h])`
2. **Permission Usage:** `sum by(permission) (rate(permission_checks_total[5m]))`
3. **API Usage by Role:** `sum by(role) (rate(http_requests_total[5m]))`
4. **Active Users:** `active_users_gauge`

---

## **PHASE 5: ALERTING RULES**

### **5.1 Alert Rules Configuration**
**Tạo file:** `monitoring/alerts/platform-rust.yml`

```yaml
groups:
  - name: platform-rust-alerts
    rules:
      # Performance Alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }}"

      # Security Alerts
      - alert: HighFailedAuthRate
        expr: rate(auth_attempts_total{success="false"}[5m]) > 10
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "High authentication failure rate"
          description: "{{ $value }} failed auth attempts per second"

      # Database Alerts
      - alert: DatabaseConnectionPoolHigh
        expr: database_active_connections / database_max_connections > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool usage high"
          description: "Connection pool usage is {{ $value | humanizePercentage }}"

      # Business Alerts
      - alert: UserRegistrationSpike
        expr: increase(user_registrations_total[1h]) > 100
        for: 5m
        labels:
          severity: info
        annotations:
          summary: "User registration spike detected"
          description: "{{ $value }} new registrations in the last hour"
```

---

## **PHASE 6: IMPLEMENTATION CHECKLIST**

### **📋 Dependencies & Files**
- [ ] **Update Cargo.toml** với metrics dependencies
- [ ] **Create metrics middleware** (`src/routes/middleware/metrics.rs`)
- [ ] **Update main.rs** với Prometheus setup
- [ ] **Create logging utility** (`src/utils/logging.rs`)
- [ ] **Update docker-compose.yml** với monitoring stack
- [ ] **Create monitoring configs** (prometheus.yml, loki.yml, etc.)
- [ ] **Create Grafana provisioning** files
- [ ] **Create dashboard JSON** files
- [ ] **Create alert rules** (prometheus alerts)

### **📁 Directory Structure**
```
platform-rust/
├── monitoring/
│   ├── prometheus.yml
│   ├── loki.yml
│   ├── promtail.yml
│   ├── alerts/
│   │   └── platform-rust.yml
│   └── grafana/
│       ├── provisioning/
│       │   ├── datasources/
│       │   │   └── datasources.yml
│       │   └── dashboards/
│       │       └── dashboard.yml
│       └── dashboards/
│           ├── api-overview.json
│           ├── authentication.json
│           ├── database.json
│           └── business-metrics.json
├── logs/ (created by app)
└── src/
    ├── routes/middleware/
    │   ├── metrics.rs (new)
    │   └── mod.rs (updated)
    ├── utils/
    │   ├── logging.rs (new)
    │   └── mod.rs (updated)
    └── main.rs (updated)
```

### **🧪 Testing Commands**
```bash
# Start monitoring stack
docker-compose up -d

# Test metrics endpoint
curl http://localhost:8386/metrics

# Test Prometheus targets
curl http://localhost:9090/api/v1/targets

# Test Grafana health
curl http://localhost:3000/api/health

# Load test to generate metrics
wrk -t12 -c400 -d30s http://localhost:8386/api/health

# Check logs
docker logs platform-rust-app
docker logs prometheus
docker logs grafana
docker logs loki
```

### **🔧 Configuration Variables**
```bash
# Environment variables for tuning
export RUST_LOG=platform_rust=info,tower_http=info
export PROMETHEUS_RETENTION=30d
export GRAFANA_ADMIN_PASSWORD=secure_password_here
export LOKI_RETENTION=744h
```

---

## **📈 Expected Outcomes**

### **Performance Benefits:**
- **Response time monitoring:** Identify slow endpoints
- **Throughput analysis:** Understand traffic patterns  
- **Error tracking:** Quick issue detection
- **Resource utilization:** Optimize memory/CPU usage

### **Security Benefits:**
- **Authentication monitoring:** Track login patterns
- **Permission audit:** Monitor RBAC usage
- **Threat detection:** Identify unusual access patterns
- **Compliance:** Security metrics for auditing

### **Business Benefits:**
- **User behavior insights:** API usage patterns
- **Growth tracking:** Registration trends
- **Feature adoption:** Endpoint popularity
- **System reliability:** Uptime monitoring

---

## **🚨 Important Notes**

### **Security Considerations:**
- **Grafana admin password** phải strong và secure
- **Prometheus metrics** chỉ nên accessible internally
- **Log data** có thể chứa sensitive information
- **Dashboard access** cần được control theo roles

### **Performance Impact:**
- **Metrics collection** overhead ~1-2% CPU
- **Log volume** cần monitor để avoid disk full
- **Retention policies** phải setup để control storage
- **Scrape intervals** cần balance between accuracy và performance

### **Maintenance:**
- **Dashboard updates** theo business requirements
- **Alert thresholds** cần tune theo actual usage
- **Log rotation** để prevent disk space issues
- **Backup strategies** cho Grafana configs và data

---

## **📞 Support & Troubleshooting**

### **Common Issues:**
1. **Metrics not showing:** Check `/metrics` endpoint và Prometheus targets
2. **Grafana dashboards empty:** Verify datasource connection
3. **High memory usage:** Adjust retention policies
4. **Log ingestion errors:** Check Promtail configuration

### **Useful Commands:**
```bash
# Restart monitoring stack
docker-compose restart prometheus grafana loki

# Check Prometheus config
docker exec prometheus promtool check config /etc/prometheus/prometheus.yml

# Grafana CLI access
docker exec -it grafana grafana-cli admin reset-admin-password newpassword

# View Loki logs
docker logs loki -f
```

---

*Tài liệu này sẽ được update theo quá trình implementation và feedback từ team.* 