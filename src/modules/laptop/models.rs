use chrono::{DateTime, NaiveDate, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;
use validator::Validate;

use crate::modules::laptop::sqlx_models::{SqlxLaptop, SqlxPrice, SqlxSpecification};
use crate::utils::pagination::PaginationMeta;
use crate::utils::validation::{
    ValidateRequestEnhanced, ValidationResult, validate_required_string, validate_string_length,
};

// ===== ENUMS =====
pub use crate::schema::{
    Currency, LaptopStatus, MarketRegion, PriceRegion, RamType, ScreenResolution,
};

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub enum GpuType {
    #[serde(rename = "Integrated")]
    Integrated,
    #[serde(rename = "Dedicated")]
    Dedicated,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub enum StorageType {
    #[serde(rename = "SSD")]
    SSD,
    #[serde(rename = "HDD")]
    HDD,
    #[serde(rename = "Hybrid")]
    Hybrid,
}

// ===== PUBLIC API MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "brand": "Apple",
    "model": "MacBook Pro",
    "full_name": "Apple MacBook Pro 14-inch M3 Pro",
    "slug": "apple-macbook-pro-14-m3-pro",
    "market_region": "Global",
    "image_urls": ["https://example.com/image1.jpg"],
    "status": "published",
    "view_count": 1250
}))]
pub struct LaptopPublicView {
    pub id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub market_region: MarketRegion,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub view_count: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LaptopDetailedView {
    pub id: Uuid,
    pub category_id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub view_count: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LaptopFullView {
    pub id: Uuid,
    pub category_id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub view_count: i64,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== SPECIFICATION MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct Specification {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub cpu_brand: String,
    pub cpu_model: String,
    pub ram_size: i32,
    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: bool,
    pub storage_type: String,
    pub storage_capacity: i32,
    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,
    pub gpu_type: String,
    pub gpu_model: Option<String>,
    pub screen_size: Decimal,
    pub screen_resolution: ScreenResolution,
    pub refresh_rate: i32,
    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== PRICE MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct Price {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub currency: Currency,
    pub source: Option<String>,
    pub region: PriceRegion,
    pub effective_date: NaiveDate,
    pub is_current: bool,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== NEW MODELS FOR CREATION =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewLaptop {
    pub id: Uuid,
    pub category_id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Option<Vec<Option<String>>>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub created_by: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewSpecification {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub cpu_brand: String,
    pub cpu_model: String,
    pub ram_size: i32,
    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: bool,
    pub storage_type: String,
    pub storage_capacity: i32,
    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,
    pub gpu_type: String,
    pub gpu_model: Option<String>,
    pub screen_size: Decimal,
    pub screen_resolution: ScreenResolution,
    pub refresh_rate: i32,
    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewPrice {
    pub id: Uuid,
    pub laptop_id: Uuid,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub currency: Currency,
    pub source: Option<String>,
    pub region: PriceRegion,
    pub effective_date: NaiveDate,
    pub is_current: bool,
    pub created_by: Uuid,
}

// ===== REQUEST MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct CreateLaptopRequest {
    #[validate(length(
        min = 1,
        max = 50,
        message = "Brand must be between 1 and 50 characters"
    ))]
    pub brand: String,

    #[validate(length(
        min = 1,
        max = 100,
        message = "Model must be between 1 and 100 characters"
    ))]
    pub model: String,

    #[validate(length(
        min = 1,
        max = 200,
        message = "Full name must be between 1 and 200 characters"
    ))]
    pub full_name: String,

    #[validate(length(
        min = 1,
        max = 250,
        message = "Slug must be between 1 and 250 characters"
    ))]
    pub slug: String,

    pub category_id: Uuid,

    #[validate(length(max = 50, message = "SKU must be at most 50 characters"))]
    pub sku: Option<String>,

    pub market_region: Option<MarketRegion>,

    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Option<Vec<String>>,
    pub is_featured: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct UpdateLaptopRequest {
    #[validate(length(
        min = 1,
        max = 50,
        message = "Brand must be between 1 and 50 characters"
    ))]
    pub brand: Option<String>,

    #[validate(length(
        min = 1,
        max = 100,
        message = "Model must be between 1 and 100 characters"
    ))]
    pub model: Option<String>,

    #[validate(length(
        min = 1,
        max = 200,
        message = "Full name must be between 1 and 200 characters"
    ))]
    pub full_name: Option<String>,

    #[validate(length(
        min = 1,
        max = 250,
        message = "Slug must be between 1 and 250 characters"
    ))]
    pub slug: Option<String>,

    pub category_id: Option<Uuid>,

    #[validate(length(max = 50, message = "SKU must be at most 50 characters"))]
    pub sku: Option<String>,

    pub market_region: Option<MarketRegion>,

    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Option<Vec<String>>,
    pub is_featured: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct CreateSpecificationRequest {
    pub laptop_id: Uuid,

    #[validate(length(
        min = 1,
        max = 20,
        message = "CPU brand must be between 1 and 20 characters"
    ))]
    pub cpu_brand: String,

    #[validate(length(
        min = 1,
        max = 100,
        message = "CPU model must be between 1 and 100 characters"
    ))]
    pub cpu_model: String,

    #[validate(range(min = 1, message = "RAM size must be at least 1 GB"))]
    pub ram_size: i32,

    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: Option<bool>,

    pub storage_type: StorageType,

    #[validate(range(min = 1, message = "Storage capacity must be at least 1 GB"))]
    pub storage_capacity: i32,

    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,

    pub gpu_type: GpuType,
    pub gpu_model: Option<String>,

    pub screen_size: Decimal,

    pub screen_resolution: ScreenResolution,

    #[validate(range(min = 1, message = "Refresh rate must be at least 1 Hz"))]
    pub refresh_rate: i32,

    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct UpdateSpecificationRequest {
    #[validate(length(
        min = 1,
        max = 20,
        message = "CPU brand must be between 1 and 20 characters"
    ))]
    pub cpu_brand: Option<String>,

    #[validate(length(
        min = 1,
        max = 100,
        message = "CPU model must be between 1 and 100 characters"
    ))]
    pub cpu_model: Option<String>,

    #[validate(range(min = 1, message = "RAM size must be at least 1 GB"))]
    pub ram_size: Option<i32>,

    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: Option<bool>,

    pub storage_type: Option<StorageType>,

    #[validate(range(min = 1, message = "Storage capacity must be at least 1 GB"))]
    pub storage_capacity: Option<i32>,

    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,

    pub gpu_type: Option<GpuType>,
    pub gpu_model: Option<String>,

    pub screen_size: Option<Decimal>,

    pub screen_resolution: Option<ScreenResolution>,

    #[validate(range(min = 1, message = "Refresh rate must be at least 1 Hz"))]
    pub refresh_rate: Option<i32>,

    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct CreatePriceRequest {
    pub laptop_id: Uuid,

    pub min_price: Decimal,

    pub max_price: Decimal,

    pub currency: Option<Currency>,

    #[validate(length(max = 100, message = "Source must be at most 100 characters"))]
    pub source: Option<String>,

    pub region: Option<PriceRegion>,

    pub effective_date: Option<NaiveDate>,
    pub is_current: Option<bool>,
}

// ===== COMPOSITE REQUEST MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
#[schema(
    example = json!({
        "brand": "Apple",
        "category_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "description": "Powerful laptop",
        "full_name": "Apple MacBook Pro 14-inch M3 Pro",
        "image_urls": ["https://example.com/image1.jpg"],
        "is_featured": true,
        "market_region": "Global",
        "model": "MacBook Pro",
        "price": {
            "min_price": 1000,
            "max_price": 2000,
            "currency": "USD",
            "source": "Apple Store",
            "region": "US",
            "effective_date": "2025-07-14",
            "is_current": true
        },
        "release_date": "2025-07-14",
        "slug": "apple-macbook-pro-14-m3-pro",
        "specification": {
            "cpu_brand": "Apple",
            "cpu_model": "M3 Pro",
            "ram_size": 16,
            "ram_type": "LPDDR5",
            "ram_slots_total": 0,
            "ram_slots_available": 0,
            "ram_max_capacity": 16,
            "ram_soldered": true,
            "storage_type": "SSD",
            "storage_capacity": 512,
            "storage_slots_total": 0,
            "storage_slots_available": 0,
            "storage_max_capacity": 512,
            "gpu_type": "Integrated",
            "gpu_model": "Apple GPU",
            "screen_size": 14.2,
            "screen_resolution": "3K",
            "refresh_rate": 120,
            "weight": 1.6,
            "operating_system": "macOS"
        }
    })
)]
pub struct CreateCompleteLaptopRequest {
    // Basic laptop information
    #[validate(length(
        min = 1,
        max = 50,
        message = "Brand must be between 1 and 50 characters"
    ))]
    pub brand: String,

    #[validate(length(
        min = 1,
        max = 100,
        message = "Model must be between 1 and 100 characters"
    ))]
    pub model: String,

    #[validate(length(
        min = 1,
        max = 200,
        message = "Full name must be between 1 and 200 characters"
    ))]
    pub full_name: String,

    #[validate(length(
        min = 1,
        max = 250,
        message = "Slug must be between 1 and 250 characters"
    ))]
    pub slug: String,

    pub category_id: Uuid,

    pub market_region: Option<MarketRegion>,

    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Option<Vec<String>>,
    pub is_featured: Option<bool>,

    // Specification information (optional)
    pub specification: Option<CreateSpecificationRequestEmbedded>,

    // Price information (optional)
    pub price: Option<CreatePriceRequestEmbedded>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct CreateSpecificationRequestEmbedded {
    #[validate(length(
        min = 1,
        max = 20,
        message = "CPU brand must be between 1 and 20 characters"
    ))]
    pub cpu_brand: String,

    #[validate(length(
        min = 1,
        max = 100,
        message = "CPU model must be between 1 and 100 characters"
    ))]
    pub cpu_model: String,

    #[validate(range(min = 1, message = "RAM size must be at least 1 GB"))]
    pub ram_size: i32,

    pub ram_type: Option<RamType>,
    pub ram_slots_total: Option<i32>,
    pub ram_slots_available: Option<i32>,
    pub ram_max_capacity: Option<i32>,
    pub ram_soldered: Option<bool>,

    pub storage_type: StorageType,

    #[validate(range(min = 1, message = "Storage capacity must be at least 1 GB"))]
    pub storage_capacity: i32,

    pub storage_slots_total: Option<i32>,
    pub storage_slots_available: Option<i32>,
    pub storage_max_capacity: Option<i32>,

    pub gpu_type: GpuType,
    pub gpu_model: Option<String>,

    pub screen_size: Decimal,

    pub screen_resolution: ScreenResolution,

    #[validate(range(min = 1, message = "Refresh rate must be at least 1 Hz"))]
    pub refresh_rate: i32,

    pub weight: Option<Decimal>,
    pub operating_system: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct CreatePriceRequestEmbedded {
    pub min_price: Decimal,

    pub max_price: Decimal,

    pub currency: Option<Currency>,

    #[validate(length(max = 100, message = "Source must be at most 100 characters"))]
    pub source: Option<String>,

    pub region: Option<PriceRegion>,

    pub effective_date: Option<NaiveDate>,
    pub is_current: Option<bool>,
}

// ===== COMPOSITE RESPONSE MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct CompleteLaptopView {
    pub laptop: LaptopFullView,
    pub specification: Option<Specification>,
    pub price: Option<Price>,
}

// ===== ENHANCED VIEW MODELS (WITH RELATED DATA) =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LaptopPublicDetailView {
    pub id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub market_region: MarketRegion,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub view_count: i64,
    pub specification: Option<Specification>,
    pub current_price: Option<Price>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LaptopFullDetailView {
    pub id: Uuid,
    pub category_id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub view_count: i64,
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub specification: Option<Specification>,
    pub current_price: Option<Price>,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedLaptopsPublicDetail {
    pub laptops: Vec<LaptopPublicDetailView>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedLaptopsFullDetail {
    pub laptops: Vec<LaptopFullDetailView>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct UpdatePriceRequest {
    pub min_price: Option<Decimal>,

    pub max_price: Option<Decimal>,

    pub currency: Option<Currency>,

    #[validate(length(max = 100, message = "Source must be at most 100 characters"))]
    pub source: Option<String>,

    pub region: Option<PriceRegion>,

    pub effective_date: Option<NaiveDate>,
    pub is_current: Option<bool>,
}

// ===== PAGINATION MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, Validate)]
pub struct LaptopPaginationRequest {
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: Option<i64>,

    #[validate(range(min = 1, max = 100, message = "Per page must be between 1 and 100"))]
    pub per_page: Option<i64>,

    pub search: Option<String>,
    pub brand: Option<String>,
    pub category_id: Option<Uuid>,
    pub status: Option<LaptopStatus>,
    pub is_featured: Option<bool>,
    pub market_region: Option<MarketRegion>,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedLaptopsPublic {
    pub laptops: Vec<LaptopPublicView>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedLaptopsDetailed {
    pub laptops: Vec<LaptopDetailedView>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedLaptopsFull {
    pub laptops: Vec<LaptopFullView>,
    pub meta: PaginationMeta,
}

impl From<SqlxLaptop> for LaptopPublicView {
    fn from(laptop: SqlxLaptop) -> Self {
        Self {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
        }
    }
}
impl From<SqlxLaptop> for LaptopDetailedView {
    fn from(laptop: SqlxLaptop) -> Self {
        Self {
            id: laptop.id,
            category_id: laptop.category_id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            sku: laptop.sku,
            market_region: laptop.market_region,
            release_date: laptop.release_date,
            description: laptop.description,
            image_urls: laptop.image_urls,
            status: laptop.status,
            is_featured: laptop.is_featured,
            view_count: laptop.view_count,
            created_at: laptop.created_at,
            updated_at: laptop.updated_at,
        }
    }
}
impl From<SqlxLaptop> for LaptopFullView {
    fn from(laptop: SqlxLaptop) -> Self {
        Self {
            id: laptop.id,
            category_id: laptop.category_id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            sku: laptop.sku,
            market_region: laptop.market_region,
            release_date: laptop.release_date,
            description: laptop.description,
            image_urls: laptop.image_urls,
            status: laptop.status,
            is_featured: laptop.is_featured,
            view_count: laptop.view_count,
            created_by: laptop.created_by,
            updated_by: laptop.updated_by,
            created_at: laptop.created_at,
            updated_at: laptop.updated_at,
        }
    }
}
impl From<SqlxSpecification> for Specification {
    fn from(spec: SqlxSpecification) -> Self {
        Self {
            id: spec.id,
            laptop_id: spec.laptop_id,
            cpu_brand: spec.cpu_brand,
            cpu_model: spec.cpu_model,
            ram_size: spec.ram_size,
            ram_type: spec.ram_type,
            ram_slots_total: spec.ram_slots_total,
            ram_slots_available: spec.ram_slots_available,
            ram_max_capacity: spec.ram_max_capacity,
            ram_soldered: spec.ram_soldered,
            storage_type: spec.storage_type,
            storage_capacity: spec.storage_capacity,
            storage_slots_total: spec.storage_slots_total,
            storage_slots_available: spec.storage_slots_available,
            storage_max_capacity: spec.storage_max_capacity,
            gpu_type: spec.gpu_type,
            gpu_model: spec.gpu_model,
            screen_size: spec.screen_size,
            screen_resolution: spec.screen_resolution,
            refresh_rate: spec.refresh_rate,
            weight: spec.weight,
            operating_system: spec.operating_system,
            created_at: spec.created_at,
            updated_at: spec.updated_at,
        }
    }
}
impl From<SqlxPrice> for Price {
    fn from(price: SqlxPrice) -> Self {
        Self {
            id: price.id,
            laptop_id: price.laptop_id,
            min_price: price.min_price,
            max_price: price.max_price,
            currency: price.currency,
            source: price.source,
            region: price.region,
            effective_date: price.effective_date,
            is_current: price.is_current,
            created_by: price.created_by,
            updated_by: price.updated_by,
            created_at: price.created_at,
            updated_at: price.updated_at,
        }
    }
}

// ===== VALIDATION IMPLEMENTATIONS =====

impl ValidateRequestEnhanced for CreateLaptopRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        validate_required_string(&self.brand, "brand", &mut result);
        validate_string_length(&self.brand, "brand", 1, 50, &mut result);

        validate_required_string(&self.model, "model", &mut result);
        validate_string_length(&self.model, "model", 1, 100, &mut result);

        validate_required_string(&self.full_name, "full_name", &mut result);
        validate_string_length(&self.full_name, "full_name", 1, 200, &mut result);

        validate_required_string(&self.slug, "slug", &mut result);
        validate_string_length(&self.slug, "slug", 1, 250, &mut result);

        if let Some(ref sku) = self.sku {
            validate_string_length(sku, "sku", 1, 50, &mut result);
        }

        result
    }
}

impl ValidateRequestEnhanced for UpdateLaptopRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        if let Some(ref brand) = self.brand {
            validate_required_string(brand, "brand", &mut result);
            validate_string_length(brand, "brand", 1, 50, &mut result);
        }

        if let Some(ref model) = self.model {
            validate_required_string(model, "model", &mut result);
            validate_string_length(model, "model", 1, 100, &mut result);
        }

        if let Some(ref full_name) = self.full_name {
            validate_required_string(full_name, "full_name", &mut result);
            validate_string_length(full_name, "full_name", 1, 200, &mut result);
        }

        if let Some(ref slug) = self.slug {
            validate_required_string(slug, "slug", &mut result);
            validate_string_length(slug, "slug", 1, 250, &mut result);
        }

        if let Some(ref sku) = self.sku {
            validate_string_length(sku, "sku", 1, 50, &mut result);
        }

        result
    }
}

impl ValidateRequestEnhanced for CreateSpecificationRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        validate_required_string(&self.cpu_brand, "cpu_brand", &mut result);
        validate_string_length(&self.cpu_brand, "cpu_brand", 1, 20, &mut result);

        validate_required_string(&self.cpu_model, "cpu_model", &mut result);
        validate_string_length(&self.cpu_model, "cpu_model", 1, 100, &mut result);

        if self.ram_size < 1 {
            result.add_error(
                "ram_size",
                "INVALID_VALUE",
                "RAM size must be at least 1 GB",
            );
        }

        if self.storage_capacity < 1 {
            result.add_error(
                "storage_capacity",
                "INVALID_VALUE",
                "Storage capacity must be at least 1 GB",
            );
        }

        if self.screen_size < rust_decimal::Decimal::new(1, 0) {
            result.add_error(
                "screen_size",
                "INVALID_VALUE",
                "Screen size must be at least 1.0 inches",
            );
        }

        if self.refresh_rate < 1 {
            result.add_error(
                "refresh_rate",
                "INVALID_VALUE",
                "Refresh rate must be at least 1 Hz",
            );
        }

        result
    }
}

impl ValidateRequestEnhanced for UpdateSpecificationRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        if let Some(ref cpu_brand) = self.cpu_brand {
            validate_required_string(cpu_brand, "cpu_brand", &mut result);
            validate_string_length(cpu_brand, "cpu_brand", 1, 20, &mut result);
        }

        if let Some(ref cpu_model) = self.cpu_model {
            validate_required_string(cpu_model, "cpu_model", &mut result);
            validate_string_length(cpu_model, "cpu_model", 1, 100, &mut result);
        }

        // Use let chains for validation with conditions
        if let Some(ram_size) = self.ram_size
            && ram_size < 1
        {
            result.add_error(
                "ram_size",
                "INVALID_VALUE",
                "RAM size must be at least 1 GB",
            );
        }

        if let Some(storage_capacity) = self.storage_capacity
            && storage_capacity < 1
        {
            result.add_error(
                "storage_capacity",
                "INVALID_VALUE",
                "Storage capacity must be at least 1 GB",
            );
        }

        if let Some(screen_size) = self.screen_size
            && screen_size < rust_decimal::Decimal::new(1, 0)
        {
            result.add_error(
                "screen_size",
                "INVALID_VALUE",
                "Screen size must be at least 1.0 inches",
            );
        }

        if let Some(refresh_rate) = self.refresh_rate
            && refresh_rate < 1
        {
            result.add_error(
                "refresh_rate",
                "INVALID_VALUE",
                "Refresh rate must be at least 1 Hz",
            );
        }

        result
    }
}

impl ValidateRequestEnhanced for CreatePriceRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        if self.min_price < rust_decimal::Decimal::ZERO {
            result.add_error(
                "min_price",
                "INVALID_VALUE",
                "Min price must be non-negative",
            );
        }

        if self.max_price < rust_decimal::Decimal::ZERO {
            result.add_error(
                "max_price",
                "INVALID_VALUE",
                "Max price must be non-negative",
            );
        }

        if self.max_price < self.min_price {
            result.add_error(
                "max_price",
                "INVALID_VALUE",
                "Max price must be greater than or equal to min price",
            );
        }

        // currency and region are now enums, no string validation needed

        if let Some(ref source) = self.source {
            validate_string_length(source, "source", 1, 100, &mut result);
        }

        // region is now an enum, no string validation needed

        result
    }
}

impl ValidateRequestEnhanced for CreateCompleteLaptopRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        // Validate laptop fields
        validate_required_string(&self.brand, "brand", &mut result);
        validate_string_length(&self.brand, "brand", 1, 50, &mut result);

        validate_required_string(&self.model, "model", &mut result);
        validate_string_length(&self.model, "model", 1, 100, &mut result);

        validate_required_string(&self.full_name, "full_name", &mut result);
        validate_string_length(&self.full_name, "full_name", 1, 200, &mut result);

        validate_required_string(&self.slug, "slug", &mut result);
        validate_string_length(&self.slug, "slug", 1, 250, &mut result);

        // market_region is now an enum, no string validation needed

        // Validate specification if provided
        if let Some(ref spec) = self.specification {
            validate_required_string(&spec.cpu_brand, "specification.cpu_brand", &mut result);
            validate_string_length(
                &spec.cpu_brand,
                "specification.cpu_brand",
                1,
                20,
                &mut result,
            );

            validate_required_string(&spec.cpu_model, "specification.cpu_model", &mut result);
            validate_string_length(
                &spec.cpu_model,
                "specification.cpu_model",
                1,
                100,
                &mut result,
            );

            if spec.ram_size < 1 {
                result.add_error(
                    "specification.ram_size",
                    "INVALID_VALUE",
                    "RAM size must be at least 1 GB",
                );
            }

            if spec.storage_capacity < 1 {
                result.add_error(
                    "specification.storage_capacity",
                    "INVALID_VALUE",
                    "Storage capacity must be at least 1 GB",
                );
            }

            if spec.screen_size < rust_decimal::Decimal::new(1, 0) {
                result.add_error(
                    "specification.screen_size",
                    "INVALID_VALUE",
                    "Screen size must be at least 1.0 inches",
                );
            }

            if spec.refresh_rate < 1 {
                result.add_error(
                    "specification.refresh_rate",
                    "INVALID_VALUE",
                    "Refresh rate must be at least 1 Hz",
                );
            }
        }

        // Validate price if provided
        if let Some(ref price) = self.price {
            if price.min_price < rust_decimal::Decimal::new(0, 0) {
                result.add_error(
                    "price.min_price",
                    "INVALID_VALUE",
                    "Min price must be at least 0",
                );
            }

            if price.max_price < rust_decimal::Decimal::new(0, 0) {
                result.add_error(
                    "price.max_price",
                    "INVALID_VALUE",
                    "Max price must be at least 0",
                );
            }

            if price.max_price < price.min_price {
                result.add_error(
                    "price.max_price",
                    "INVALID_VALUE",
                    "Max price must be greater than or equal to min price",
                );
            }

            // currency and region are now enums, no string validation needed

            // Use let chains for nested if let
            if let Some(ref source) = price.source {
                validate_string_length(source, "price.source", 0, 100, &mut result);
            }

            // region is now an enum, no string validation needed
        }

        result
    }
}

impl ValidateRequestEnhanced for CreateSpecificationRequestEmbedded {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        validate_required_string(&self.cpu_brand, "cpu_brand", &mut result);
        validate_string_length(&self.cpu_brand, "cpu_brand", 1, 20, &mut result);

        validate_required_string(&self.cpu_model, "cpu_model", &mut result);
        validate_string_length(&self.cpu_model, "cpu_model", 1, 100, &mut result);

        if self.ram_size < 1 {
            result.add_error(
                "ram_size",
                "INVALID_VALUE",
                "RAM size must be at least 1 GB",
            );
        }

        if self.storage_capacity < 1 {
            result.add_error(
                "storage_capacity",
                "INVALID_VALUE",
                "Storage capacity must be at least 1 GB",
            );
        }

        if self.screen_size < rust_decimal::Decimal::new(1, 0) {
            result.add_error(
                "screen_size",
                "INVALID_VALUE",
                "Screen size must be at least 1.0 inches",
            );
        }

        if self.refresh_rate < 1 {
            result.add_error(
                "refresh_rate",
                "INVALID_VALUE",
                "Refresh rate must be at least 1 Hz",
            );
        }

        result
    }
}

impl ValidateRequestEnhanced for CreatePriceRequestEmbedded {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        if self.min_price < rust_decimal::Decimal::new(0, 0) {
            result.add_error("min_price", "INVALID_VALUE", "Min price must be at least 0");
        }

        if self.max_price < rust_decimal::Decimal::new(0, 0) {
            result.add_error("max_price", "INVALID_VALUE", "Max price must be at least 0");
        }

        if self.max_price < self.min_price {
            result.add_error(
                "max_price",
                "INVALID_VALUE",
                "Max price must be greater than or equal to min price",
            );
        }

        // currency and region are now enums, no string validation needed

        if let Some(ref source) = self.source {
            validate_string_length(source, "source", 0, 100, &mut result);
        }

        // region is now an enum, no string validation needed

        result
    }
}

impl ValidateRequestEnhanced for UpdatePriceRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        // Check if at least one field is provided for update
        let has_any_field = self.min_price.is_some()
            || self.max_price.is_some()
            || self.currency.is_some()
            || self.source.is_some()
            || self.region.is_some()
            || self.effective_date.is_some()
            || self.is_current.is_some();

        if !has_any_field {
            result.add_error(
                "update",
                "REQUIRED",
                "At least one field must be provided for update",
            );
        }

        // Validate price logic using let chains
        if let (Some(min), Some(max)) = (self.min_price, self.max_price)
            && min > max
        {
            result.add_error(
                "max_price",
                "INVALID_VALUE",
                "Maximum price cannot be less than minimum price",
            );
        }

        result
    }
}

// ===== TYPE ALIASES FOR CONSISTENT PUBLIC/PRIVATE API PATTERN =====

/// Type alias for laptop public view to maintain consistency with category pattern
pub type LaptopPrivateView = LaptopFullView;

/// Type alias for paginated laptop public view
pub type PaginatedLaptopsPublicView = PaginatedLaptopsPublic;

/// Type alias for paginated laptop private view (with full access)
pub type PaginatedLaptopsPrivateView = PaginatedLaptopsFull;

// ===== HELPER CONVERSION FUNCTIONS =====

/// Convert any laptop model to public view (for consistency)
impl From<LaptopDetailedView> for LaptopPublicView {
    fn from(laptop: LaptopDetailedView) -> Self {
        Self {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
        }
    }
}

/// Convert full view to public view (for consistency)
impl From<LaptopFullView> for LaptopPublicView {
    fn from(laptop: LaptopFullView) -> Self {
        Self {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
        }
    }
}
