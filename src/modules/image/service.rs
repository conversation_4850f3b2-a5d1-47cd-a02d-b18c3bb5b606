use crate::{
    config::CloudinaryConfig,
    errors::{AppError, Result},
    modules::image::{
        models::{
            ImageDeletionRequest, ImageDeletionResponse, ImageSignatureRequest,
            ImageSignatureResponse,
        },
        validation::ImageValidation,
    },
};
use async_trait::async_trait;
use sha1::{Digest, Sha1};
use std::collections::HashMap;

#[async_trait]
pub trait ImageServiceTrait: Send + Sync {
    async fn generate_signature(
        &self,
        request: ImageSignatureRequest,
    ) -> Result<ImageSignatureResponse>;
    async fn validate_cloudinary_url(&self, url: &str, resource_type: &str) -> Result<bool>;
    async fn extract_public_id_from_url(&self, url: &str) -> Result<String>;
    async fn delete_image(&self, request: ImageDeletionRequest) -> Result<ImageDeletionResponse>;
    async fn health_check(&self) -> Result<CloudinaryHealthStatus>;
    async fn verify_connection(&self) -> Result<()>;
}

#[derive(Debug, <PERSON><PERSON>, serde::Serialize, serde::Deserialize, utoipa::ToSchema)]
pub struct CloudinaryHealthStatus {
    pub status: String,
    pub cloud_name: String,
    pub api_accessible: bool,
    pub response_time_ms: Option<u64>,
    pub error_message: Option<String>,
}

pub struct ImageService {
    config: CloudinaryConfig,
    http_client: reqwest::Client,
}

impl ImageService {
    pub fn new(config: CloudinaryConfig) -> Self {
        Self {
            config,
            http_client: reqwest::Client::new(),
        }
    }

    /// Verify Cloudinary connection and credentials
    pub async fn verify_connection(&self) -> Result<()> {
        tracing::info!(
            "Verifying Cloudinary connection for cloud: {}",
            self.config.cloud_name
        );

        let health_status = self.health_check().await?;

        if health_status.api_accessible {
            tracing::info!(
                "Cloudinary connection verified successfully. Response time: {}ms",
                health_status.response_time_ms.unwrap_or(0)
            );
            Ok(())
        } else {
            let error_msg = health_status
                .error_message
                .unwrap_or_else(|| "Unknown error".to_string());
            Err(AppError::Internal(anyhow::anyhow!(
                "Cloudinary connection failed: {}",
                error_msg
            )))
        }
    }

    /// Generate SHA1 signature for Cloudinary upload
    fn generate_cloudinary_signature(&self, params: &HashMap<String, String>) -> String {
        // Sort parameters alphabetically
        let mut sorted_params: Vec<_> = params.iter().collect();
        sorted_params.sort_by_key(|&(k, _)| k);

        // Create parameter string
        let params_string = sorted_params
            .iter()
            .map(|(k, v)| format!("{}={}", k, v))
            .collect::<Vec<_>>()
            .join("&");

        // Append API secret
        let signature_string = format!("{}{}", params_string, self.config.api_secret);

        // Generate SHA1 hash
        let mut hasher = Sha1::new();
        hasher.update(signature_string.as_bytes());
        hex::encode(hasher.finalize())
    }

    /// Build parameters for signature generation
    fn build_signature_params(
        &self,
        request: &ImageSignatureRequest,
        timestamp: i64,
    ) -> Result<HashMap<String, String>> {
        let mut params = HashMap::new();

        // Required parameters
        params.insert("timestamp".to_string(), timestamp.to_string());
        params.insert("folder".to_string(), request.folder.clone());

        // Optional parameters
        if let Some(ref tags) = request.tags {
            if !tags.is_empty() {
                params.insert("tags".to_string(), tags.join(","));
            }
        }

        if let Some(ref upload_preset) = self.config.upload_preset {
            params.insert("upload_preset".to_string(), upload_preset.clone());
        }

        if let Some(max_file_size) = request.max_file_size {
            ImageValidation::validate_file_size(max_file_size)?;
            params.insert("max_file_size".to_string(), max_file_size.to_string());
        }

        if let Some(ref formats) = request.allowed_formats {
            ImageValidation::validate_image_formats(formats)?;
            params.insert("allowed_formats".to_string(), formats.join(","));
        }

        Ok(params)
    }
}

#[async_trait]
impl ImageServiceTrait for ImageService {
    async fn generate_signature(
        &self,
        request: ImageSignatureRequest,
    ) -> Result<ImageSignatureResponse> {
        tracing::info!(
            "Generating signature for resource_type: {}, folder: {}",
            request.resource_type,
            request.folder
        );

        // Validate request
        ImageValidation::validate_resource_type(&request.resource_type)?;
        ImageValidation::validate_folder_permission(&request.resource_type, &request.folder)?;

        // Generate timestamp
        let timestamp = chrono::Utc::now().timestamp();

        // Build parameters
        let params = self.build_signature_params(&request, timestamp)?;

        // Generate signature
        let signature = self.generate_cloudinary_signature(&params);

        tracing::info!(
            "Signature generated successfully for folder: {}",
            request.folder
        );

        Ok(ImageSignatureResponse {
            signature,
            timestamp,
            api_key: self.config.api_key.clone(),
            cloud_name: self.config.cloud_name.clone(),
            folder: request.folder,
            tags: request.tags.map(|tags| tags.join(",")),
            upload_preset: self.config.upload_preset.clone(),
        })
    }

    async fn validate_cloudinary_url(&self, url: &str, resource_type: &str) -> Result<bool> {
        tracing::debug!(
            "Validating URL: {} for resource_type: {}",
            url,
            resource_type
        );

        // Validate resource type
        ImageValidation::validate_resource_type(resource_type)?;

        // Validate URL format
        if let Err(_) =
            ImageValidation::validate_cloudinary_url_format(url, &self.config.cloud_name)
        {
            return Ok(false);
        }

        // Validate resource type matches URL path
        let is_valid = match resource_type {
            "laptop" => url.contains("/laptops/"),
            "category" => url.contains("/categories/"),
            "user" => url.contains("/users/"),
            _ => false,
        };

        tracing::debug!("URL validation result: {} for {}", is_valid, url);
        Ok(is_valid)
    }

    async fn extract_public_id_from_url(&self, url: &str) -> Result<String> {
        tracing::debug!("Extracting public_id from URL: {}", url);

        let public_id = ImageValidation::extract_public_id_from_url(url)?;

        tracing::debug!("Extracted public_id: {}", public_id);
        Ok(public_id)
    }

    async fn delete_image(&self, request: ImageDeletionRequest) -> Result<ImageDeletionResponse> {
        tracing::info!(
            "Deleting image with public_id: {} for resource_type: {}",
            request.public_id,
            request.resource_type
        );

        // Validate resource type
        ImageValidation::validate_resource_type(&request.resource_type)?;

        // Build deletion parameters
        let timestamp = chrono::Utc::now().timestamp();
        let mut params = HashMap::new();
        params.insert("public_id".to_string(), request.public_id.clone());
        params.insert("timestamp".to_string(), timestamp.to_string());

        // Generate signature for deletion
        let signature = self.generate_cloudinary_signature(&params);

        // Prepare deletion request
        let deletion_url = format!(
            "https://api.cloudinary.com/v1_1/{}/image/destroy",
            self.config.cloud_name
        );

        let mut form_data = HashMap::new();
        form_data.insert("public_id", request.public_id.clone());
        form_data.insert("timestamp", timestamp.to_string());
        form_data.insert("signature", signature);
        form_data.insert("api_key", self.config.api_key.clone());

        // Make deletion request to Cloudinary
        let response = self
            .http_client
            .post(&deletion_url)
            .form(&form_data)
            .send()
            .await
            .map_err(|e| {
                tracing::error!("Failed to send deletion request: {}", e);
                AppError::Internal(anyhow::anyhow!("Failed to delete image: {}", e))
            })?;

        if response.status().is_success() {
            let _response_text = response.text().await.map_err(|e| {
                tracing::error!("Failed to read deletion response: {}", e);
                AppError::Internal(anyhow::anyhow!("Failed to read deletion response: {}", e))
            })?;

            tracing::info!("Image deleted successfully: {}", request.public_id);

            Ok(ImageDeletionResponse {
                success: true,
                message: "Image deleted successfully".to_string(),
                deleted_public_id: Some(request.public_id),
            })
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            tracing::error!(
                "Failed to delete image {}: {}",
                request.public_id,
                error_text
            );

            Ok(ImageDeletionResponse {
                success: false,
                message: format!("Failed to delete image: {}", error_text),
                deleted_public_id: None,
            })
        }
    }

    async fn health_check(&self) -> Result<CloudinaryHealthStatus> {
        let start_time = std::time::Instant::now();

        // Use a simple API call to check connectivity
        // We'll use the resources endpoint with minimal parameters
        let url = format!(
            "https://api.cloudinary.com/v1_1/{}/resources/image",
            self.config.cloud_name
        );

        let response_result = self
            .http_client
            .get(&url)
            .basic_auth(&self.config.api_key, Some(&self.config.api_secret))
            .query(&[("max_results", "1")]) // Minimal request
            .timeout(std::time::Duration::from_secs(10))
            .send()
            .await;

        let response_time_ms = start_time.elapsed().as_millis() as u64;

        match response_result {
            Ok(response) => {
                if response.status().is_success() {
                    Ok(CloudinaryHealthStatus {
                        status: "healthy".to_string(),
                        cloud_name: self.config.cloud_name.clone(),
                        api_accessible: true,
                        response_time_ms: Some(response_time_ms),
                        error_message: None,
                    })
                } else {
                    let status_code = response.status();
                    let error_text = response
                        .text()
                        .await
                        .unwrap_or_else(|_| "Failed to read error response".to_string());

                    let error_message = format!("HTTP {}: {}", status_code, error_text);

                    Ok(CloudinaryHealthStatus {
                        status: "unhealthy".to_string(),
                        cloud_name: self.config.cloud_name.clone(),
                        api_accessible: false,
                        response_time_ms: Some(response_time_ms),
                        error_message: Some(error_message),
                    })
                }
            }
            Err(e) => {
                let error_message = if e.is_timeout() {
                    "Request timeout (>10s)".to_string()
                } else if e.is_connect() {
                    "Connection failed - check internet connectivity".to_string()
                } else {
                    format!("Request failed: {}", e)
                };

                Ok(CloudinaryHealthStatus {
                    status: "unhealthy".to_string(),
                    cloud_name: self.config.cloud_name.clone(),
                    api_accessible: false,
                    response_time_ms: Some(response_time_ms),
                    error_message: Some(error_message),
                })
            }
        }
    }

    async fn verify_connection(&self) -> Result<()> {
        tracing::info!(
            "Verifying Cloudinary connection for cloud: {}",
            self.config.cloud_name
        );

        let health_status = self.health_check().await?;

        if health_status.api_accessible {
            tracing::info!(
                "Cloudinary connection verified successfully. Response time: {}ms",
                health_status.response_time_ms.unwrap_or(0)
            );
            Ok(())
        } else {
            let error_msg = health_status
                .error_message
                .unwrap_or_else(|| "Unknown error".to_string());
            Err(AppError::Internal(anyhow::anyhow!(
                "Cloudinary connection failed: {}",
                error_msg
            )))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> CloudinaryConfig {
        CloudinaryConfig {
            cloud_name: "test_cloud".to_string(),
            api_key: "test_key".to_string(),
            api_secret: "test_secret".to_string(),
            upload_preset: Some("test_preset".to_string()),
            secure: true,
        }
    }

    fn create_test_service() -> ImageService {
        ImageService::new(create_test_config())
    }

    #[test]
    fn test_generate_cloudinary_signature() {
        let service = create_test_service();
        let mut params = HashMap::new();
        params.insert("timestamp".to_string(), "**********".to_string());
        params.insert("folder".to_string(), "laptops/test".to_string());

        let signature = service.generate_cloudinary_signature(&params);

        // The signature should be a 40-character hex string (SHA1)
        assert_eq!(signature.len(), 40);
        assert!(signature.chars().all(|c| c.is_ascii_hexdigit()));
    }

    #[tokio::test]
    async fn test_generate_signature() {
        let service = create_test_service();
        let request = ImageSignatureRequest {
            resource_type: "laptop".to_string(),
            folder: "laptops/test-123".to_string(),
            tags: Some(vec!["test".to_string(), "laptop".to_string()]),
            max_file_size: Some(5000000), // 5MB
            allowed_formats: Some(vec!["jpg".to_string(), "png".to_string()]),
        };

        let result = service.generate_signature(request).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.cloud_name, "test_cloud");
        assert_eq!(response.api_key, "test_key");
        assert_eq!(response.folder, "laptops/test-123");
        assert!(response.signature.len() == 40); // SHA1 hex string
    }

    #[tokio::test]
    async fn test_validate_cloudinary_url() {
        let service = create_test_service();

        // Valid URL
        let valid_url =
            "https://res.cloudinary.com/test_cloud/image/upload/v**********/laptops/test/image.jpg";
        let result = service.validate_cloudinary_url(valid_url, "laptop").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Invalid URL (wrong domain)
        let invalid_url = "https://example.com/image.jpg";
        let result = service.validate_cloudinary_url(invalid_url, "laptop").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());

        // Invalid URL (wrong resource type)
        let wrong_type_url = "https://res.cloudinary.com/test_cloud/image/upload/v**********/categories/test/image.jpg";
        let result = service
            .validate_cloudinary_url(wrong_type_url, "laptop")
            .await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_extract_public_id_from_url() {
        let service = create_test_service();

        let url = "https://res.cloudinary.com/test_cloud/image/upload/v**********/laptops/test-123/image.jpg";
        let result = service.extract_public_id_from_url(url).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "laptops/test-123/image");
    }
}
