use crate::{
    constants::API_CATEGORIES_PATH,
    errors::Result,
    modules::category::{
        models::{
            CategoryPaginationRequest, CategoryPrivateView, CategoryPublicView,
            CategoryWithTracking, CreateCategoryRequest, PaginatedCategoriesPrivateView,
            PaginatedCategoriesPublicView, PaginatedCategoriesWithTracking, UpdateCategoryRequest,
        },
        traits::CategoryServiceTrait,
    },
    response::{ApiResponse, ApiResponseJson},
    routes::middleware::{ApiType, auth::AuthenticatedUser},
    utils::response_helpers::{ResponseHelper, response_constants::category},
};
use axum::{
    Extension, Json,
    extract::{Path, Query, State},
    response::IntoResponse,
};
use std::sync::Arc;
use uuid::Uuid;
use utoipa::OpenApi;

#[derive(OpenApi)]
#[openapi(
    paths(create_category, get_category, get_categories_with_pagination, get_categories_by_type, update_category, delete_category, toggle_category_status, get_categories_shared, get_category_by_id_shared),
    components(schemas(
        CategoryPaginationRequest, CategoryPrivateView, CategoryPublicView,
        CategoryWithTracking, CreateCategoryRequest, PaginatedCategoriesPrivateView,
        PaginatedCategoriesPublicView, PaginatedCategoriesWithTracking, UpdateCategoryRequest
    )),
    tags((name = "Categories", description = "Category management endpoints"))
)]
pub struct CategoryApiDoc;

#[utoipa::path(
    post,
    path = "/api/categories",
    tag = "Categories",
    request_body = crate::modules::category::models::CreateCategoryRequest,
    responses(
        (status = 201, description = "Category created successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Category already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(create_request): Json<CreateCategoryRequest>,
) -> Result<impl axum::response::IntoResponse> {
    // Extract user ID from authenticated user
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| crate::errors::AppError::Unauthorized("Invalid user ID".into()))?;

    let category = category_service
        .create_category(create_request, &user_id)
        .await?;

    Ok(ResponseHelper::entity_created(
        API_CATEGORIES_PATH,
        category::SUCCESS_CREATE,
        category::MSG_CREATED,
        category,
    ))
}

#[utoipa::path(
    get,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category retrieved successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 404, description = "Category not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<axum::response::Response> {
    let path = format!("{API_CATEGORIES_PATH}/{id}");
    let category = crate::handle_service_result!(category_service.get_category_by_id(&id), path);

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&id.to_string()),
        category::SUCCESS_GET,
        category::MSG_RETRIEVED,
        category,
    )
    .into_response())
}

#[utoipa::path(
    get,
    path = "/api/categories",
    tag = "Categories",
    params(
        CategoryPaginationRequest
    ),
    responses(
        (status = 200, description = "Categories retrieved successfully", body = ApiResponse<PaginatedCategoriesWithTracking>),
        (status = 400, description = "Invalid pagination parameters")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_categories_with_pagination(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Query(pagination_request): Query<CategoryPaginationRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let categories = category_service
        .get_categories_with_pagination_and_tracking(pagination_request)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        None,
        category::SUCCESS_LIST,
        category::MSG_LIST_RETRIEVED,
        categories,
    ))
}

#[utoipa::path(
    get,
    path = "/api/categories/type/{category_type}",
    tag = "Categories",
    params(
        ("category_type" = String, Path, description = "Category type (e.g., 'laptops', 'phones')")
    ),
    responses(
        (status = 200, description = "Categories retrieved successfully", body = ApiResponse<Vec<CategoryWithTracking>>),
        (status = 400, description = "Invalid category type")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_categories_by_type(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(category_type): Path<String>,
) -> Result<impl axum::response::IntoResponse> {
    let categories = category_service
        .get_categories_by_type_with_tracking(&category_type)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&format!("type/{category_type}")),
        category::SUCCESS_LIST,
        category::MSG_LIST_RETRIEVED,
        categories,
    ))
}

#[utoipa::path(
    put,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    request_body = crate::modules::category::models::UpdateCategoryRequest,
    responses(
        (status = 200, description = "Category updated successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Category not found"),
        (status = 409, description = "Category name already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(update_request): Json<UpdateCategoryRequest>,
) -> Result<impl axum::response::IntoResponse> {
    // Extract user ID from authenticated user
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| crate::errors::AppError::Unauthorized("Invalid user ID".into()))?;

    let category = category_service
        .update_category(&id, update_request, &user_id)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&id.to_string()),
        category::SUCCESS_UPDATE,
        category::MSG_UPDATED,
        category,
    ))
}

#[utoipa::path(
    delete,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category deleted successfully", body = ApiResponseJson),
        (status = 404, description = "Category not found"),
        (status = 409, description = "Category is being used and cannot be deleted")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_category(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    category_service.delete_category(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_CATEGORIES_PATH,
        &id.to_string(),
        category::SUCCESS_DELETE,
        category::MSG_DELETED,
    ))
}

#[utoipa::path(
    patch,
    path = "/api/categories/{id}/toggle-status",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category status toggled successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 404, description = "Category not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn toggle_category_status(
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
    Extension(user): Extension<AuthenticatedUser>,
) -> Result<impl axum::response::IntoResponse> {
    // Extract user ID from authenticated user
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| crate::errors::AppError::Unauthorized("Invalid user ID".into()))?;

    let category = category_service
        .toggle_category_status(&id, &user_id)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_CATEGORIES_PATH,
        Some(&format!("{id}/toggle-status")),
        category::SUCCESS_UPDATE,
        "Category status toggled successfully",
        category,
    ))
}

// ===== SHARED HANDLERS (Support both Public and Private API via header) =====

/// Get categories with pagination
/// - Public API: Returns categories with limited information
/// - Private API: Returns categories with full tracking information for authenticated users
#[utoipa::path(
    get,
    path = "/api/categories",
    tag = "Categories",
    params(
        CategoryPaginationRequest
    ),
    responses(
        (status = 200, description = "Categories retrieved successfully"),
        (status = 400, description = "Invalid pagination parameters"),
        (status = 401, description = "Authentication required for private API access")
    )
)]
pub async fn get_categories_shared(
    Extension(api_type): Extension<ApiType>,
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Query(pagination_request): Query<CategoryPaginationRequest>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<axum::response::Response> {
    match api_type {
        ApiType::Public => {
            // Public API: return limited information
            let categories = category_service
                .get_categories_with_pagination_and_tracking(pagination_request)
                .await?;

            // Convert to public view
            let public_categories = PaginatedCategoriesPublicView {
                categories: categories
                    .categories
                    .into_iter()
                    .map(CategoryPublicView::from)
                    .collect(),
                meta: categories.meta,
            };

            Ok(ResponseHelper::entity_retrieved(
                API_CATEGORIES_PATH,
                None,
                category::SUCCESS_LIST,
                category::MSG_LIST_RETRIEVED,
                public_categories,
            )
            .into_response())
        }
        ApiType::Private => {
            // Private API: requires authentication
            if user.is_none() {
                return Err(crate::errors::AppError::Unauthorized(
                    "Authentication required for private API access".into(),
                ));
            }

            // Get full categories with tracking information
            let categories = category_service
                .get_categories_with_pagination_and_tracking(pagination_request)
                .await?;

            // Convert to private view
            let private_categories = PaginatedCategoriesPrivateView {
                categories: categories
                    .categories
                    .into_iter()
                    .map(CategoryPrivateView::from)
                    .collect(),
                meta: categories.meta,
            };

            Ok(ResponseHelper::entity_retrieved(
                API_CATEGORIES_PATH,
                None,
                category::SUCCESS_LIST,
                category::MSG_LIST_RETRIEVED,
                private_categories,
            )
            .into_response())
        }
    }
}

/// Get category by ID
/// - Public API (no header or X-API-Type: public): Returns category without authentication
/// - Private API (X-API-Type: private + auth): Returns category with authentication
#[utoipa::path(
    get,
    path = "/api/categories/{id}",
    tag = "Categories",
    params(
        ("id" = String, Path, description = "Category ID")
    ),
    responses(
        (status = 200, description = "Category retrieved successfully", body = ApiResponse<CategoryWithTracking>),
        (status = 404, description = "Category not found")
    )
)]
pub async fn get_category_by_id_shared(
    Extension(api_type): Extension<ApiType>,
    State(category_service): State<Arc<dyn CategoryServiceTrait>>,
    Path(id): Path<Uuid>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<axum::response::Response> {
    match api_type {
        ApiType::Public => {
            // Public API: return limited information
            let path = format!("{API_CATEGORIES_PATH}/{id}");
            let category =
                crate::handle_service_result!(category_service.get_category_by_id(&id), path);

            // Convert to public view
            let public_category = CategoryPublicView::from(category);

            Ok(ResponseHelper::entity_retrieved(
                API_CATEGORIES_PATH,
                Some(&id.to_string()),
                category::SUCCESS_GET,
                category::MSG_RETRIEVED,
                public_category,
            )
            .into_response())
        }
        ApiType::Private => {
            // Private API: requires authentication
            if user.is_none() {
                return Err(crate::errors::AppError::Unauthorized(
                    "Authentication required for private API access".into(),
                ));
            }

            // Get category with full tracking information
            let path = format!("{API_CATEGORIES_PATH}/{id}");
            let category =
                crate::handle_service_result!(category_service.get_category_by_id(&id), path);

            // Convert to private view
            let private_category = CategoryPrivateView::from(category);

            Ok(ResponseHelper::entity_retrieved(
                API_CATEGORIES_PATH,
                Some(&id.to_string()),
                category::SUCCESS_GET,
                category::MSG_RETRIEVED,
                private_category,
            )
            .into_response())
        }
    }
}
