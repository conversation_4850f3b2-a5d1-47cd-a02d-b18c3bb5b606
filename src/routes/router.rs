#![allow(clippy::redundant_clone)]
use crate::routes::middleware::rate_limit;
use crate::{
    config::Config,
    container::ServiceContainer,
    errors::AppError,
    handlers::fallback::fallback_handler,
    modules::{
        auth::service_trait::AuthServiceTrait, category::service_trait::CategoryServiceTrait,
        email::EmailEventBus, laptop::service_trait::LaptopManagementServiceTrait,
    },
    routes::middleware::{
        compression::{CompressionConfig, create_compression_layer},
        create_permissive_cors_layer, enhanced_request_tracing_middleware,
        json_rejection_handler::json_rejection_handler,
        private_api_middleware, require_permission, security_headers_middleware,
    },
};
use axum::extract::State;
use axum::{Router, middleware};
use chrono;
use serde_json;
use std::sync::Arc;
use tower::ServiceBuilder;

use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

// Merge OpenAPI docs from modules
#[derive(OpenApi)]
#[openapi(
    paths(
        // Thêm paths từ các handlers n<PERSON><PERSON> c<PERSON>n, nhưng utoipa sẽ tự merge
    ),
    components(
        // Components từ modules
    ),
    modifiers(&MergeApiDocs)
)]
pub struct ApiDoc;

struct MergeApiDocs;

impl utoipa::Modify for MergeApiDocs {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        let docs = vec![
            crate::modules::auth::handler::ApiDoc::openapi(),
            crate::modules::image::handler::ImageApiDoc::openapi(),
            crate::modules::permission::handler::PermissionApiDoc::openapi(),
            crate::modules::role::handler::RoleApiDoc::openapi(),
            crate::modules::user::handlers::user_handler::UserApiDoc::openapi(),
            crate::modules::laptop::handler::LaptopApiDoc::openapi(),
            crate::modules::category::handler::CategoryApiDoc::openapi(),
        ];
        for doc in docs {
            openapi.merge(doc.clone());
        }
    }
}

/// Main router factory với DI container
pub async fn create_routes(
    container: Arc<ServiceContainer>,
    config: Config,
    _email_event_bus: Arc<EmailEventBus>,
) -> Result<Router, AppError> {
    // Use existing service container (FIXED: ServiceContainer duplication issue)

    let public_routes = create_public_routes(&container);
    let private_routes = create_private_routes(&container);

    // Combine routes
    let api_routes = public_routes
        .merge(private_routes)
        // Apply global rate-limit middleware (10 req/s per IP)
        .layer(middleware::from_fn(rate_limit::rate_limit_middleware));

    let health_routes = Router::new()
        .route(
            "/health/detailed",
            axum::routing::get(detailed_health_check),
        )
        .with_state(container);

    // Create CORS layer based on environment
    let cors_layer = if cfg!(debug_assertions) {
        // Development mode: more permissive
        create_permissive_cors_layer()
    } else {
        // Production mode: use config
        crate::routes::middleware::cors::create_cors_layer(&config.cors)
    };

    // Main router
    Ok(Router::new()
        .nest("/api", api_routes)
        .route("/health", axum::routing::get(health_check))
        .merge(health_routes)
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi()))
        .fallback(fallback_handler)
        .layer(
            ServiceBuilder::new()
                .layer(cors_layer)
                .layer(create_compression_layer(CompressionConfig::default()))
                .layer(middleware::from_fn(enhanced_request_tracing_middleware))
                .layer(middleware::from_fn(security_headers_middleware))
                .layer(middleware::from_fn(json_rejection_handler))
                .into_inner(),
        ))
}

/// Public routes (no authentication required)
fn create_public_routes(container: &Arc<ServiceContainer>) -> Router {
    let auth_routes = AuthRouter::new(container.auth_service()).create_routes(container);
    let laptop_routes =
        LaptopRouter::new(container.laptop_management_service()).create_public_routes(container);
    let category_public_routes =
        CategoryRouter::new(container.category_service()).create_public_routes(container);
    let progression_routes =
        ProgressionRouter::new(container.exp_system_service()).create_routes(container);
    let redis_routes = RedisRouter::new().create_routes(container);

    Router::new()
        .merge(auth_routes)
        .merge(laptop_routes)
        .merge(category_public_routes)
        .merge(progression_routes)
        .merge(redis_routes)
}

/// Private routes (authentication required)
fn create_private_routes(container: &Arc<ServiceContainer>) -> Router {
    // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
    let container_arc = Arc::clone(container);

    let permission_routes =
        PermissionRouter::new(container.permission_service()).create_routes(container);
    let role_routes = RoleRouter::new(container.role_service()).create_routes(container);
    let user_routes = UserRouter::new(container.user_service()).create_routes(container);
    let laptop_routes =
        LaptopRouter::new(container.laptop_management_service()).create_private_routes(container);
    let category_private_routes =
        CategoryRouter::new(container.category_service()).create_private_routes(container);
    let profile_routes = ProfileRouter::new(container.auth_service()).create_routes(container);
    let image_routes = ImageRouter::new().create_routes(container);

    // Permission matrix route (needs role service, placed here to avoid circular dependencies)
    let permission_matrix_route = Router::new()
        .route(
            "/permissions/matrix",
            axum::routing::put(crate::modules::role::handler::update_permission_matrix)
                .route_layer(middleware::from_fn_with_state(
                    Arc::clone(container),
                    require_permission("permissions:update"),
                )),
        )
        .with_state(container.role_service().clone());

    Router::new()
        .merge(permission_routes)
        .merge(role_routes)
        .merge(user_routes)
        .merge(laptop_routes)
        .merge(category_private_routes)
        .merge(profile_routes)
        .merge(image_routes)
        .merge(permission_matrix_route)
        .layer(middleware::from_fn_with_state(
            container_arc,
            private_api_middleware,
        ))
}

/// Auth Router (SOLID: Single Responsibility)
struct AuthRouter {
    auth_service: Arc<dyn AuthServiceTrait>,
}

impl AuthRouter {
    fn new(auth_service: Arc<dyn AuthServiceTrait>) -> Self {
        Self { auth_service }
    }

    fn create_routes(self, _container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::auth::handler as auth_handler;
        use crate::modules::auth::handler::OAuthHandlerState;
        use crate::modules::auth::services::SessionStorageManager;
        use axum::routing::{get, post};

        let auth_service_shared = self.auth_service;

        // Standard auth routes with auth_service state
        let standard_auth_routes = Router::new()
            .route("/auth/login", post(auth_handler::login))
            .route("/auth/register", post(auth_handler::register))
            .route("/auth/refresh", post(auth_handler::refresh_token))
            .with_state(auth_service_shared.clone());

        // OAuth routes with their own state
        // Use SessionStorageManager to prevent memory leaks from background cleanup task
        let session_storage_manager = SessionStorageManager::new();
        let session_storage = session_storage_manager.storage();

        let oauth_state = OAuthHandlerState {
            auth_service: auth_service_shared,
            session_storage,
        };

        let oauth_routes = Router::new()
            .route("/auth/oauth/{provider}", get(auth_handler::oauth_login))
            .route(
                "/auth/oauth/{provider}/callback",
                get(auth_handler::oauth_callback),
            )
            .with_state(oauth_state);

        // Merge all auth routes and apply rate limiting
        standard_auth_routes
            .merge(oauth_routes)
            .layer(middleware::from_fn(rate_limit::auth_rate_limit_middleware))
    }
}

/// User Router (SOLID: Single Responsibility)
struct UserRouter {
    user_service:
        std::sync::Arc<dyn crate::modules::user::traits::service_traits::UserServiceTrait>,
}

impl UserRouter {
    fn new(
        user_service: std::sync::Arc<
            dyn crate::modules::user::traits::service_traits::UserServiceTrait,
        >,
    ) -> Self {
        Self { user_service }
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::user::handlers;
        use axum::routing::{delete, get, post, put};

        // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
        let container_for_middleware = Arc::clone(container);

        Router::new()
            .route(
                "/users",
                get(handlers::get_users_with_pagination).route_layer(
                    middleware::from_fn_with_state(
                        container_for_middleware.clone(),
                        require_permission("users:read"),
                    ),
                ),
            )
            .route(
                "/users",
                post(handlers::create_user).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("users:create"),
                )),
            )
            .route(
                "/users/{id}",
                get(handlers::get_user).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("users:read"),
                )),
            )
            .route(
                "/users/{id}",
                put(handlers::update_user).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("users:update"),
                )),
            )
            .route(
                "/users/{id}",
                delete(handlers::delete_user).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("users:delete"),
                )),
            )
            .route(
                "/users/{id}/roles",
                put(handlers::update_user_roles).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("users:update"),
                )),
            )
            .route(
                "/users/{id}/roles",
                get(handlers::get_user_roles).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("users:read"),
                )),
            )
            .with_state(self.user_service.clone())
    }
}

/// Permission Router (SOLID: Single Responsibility)
struct PermissionRouter {
    permission_service:
        std::sync::Arc<dyn crate::modules::permission::service_trait::PermissionServiceTrait>,
}

impl PermissionRouter {
    fn new(
        permission_service: std::sync::Arc<
            dyn crate::modules::permission::service_trait::PermissionServiceTrait,
        >,
    ) -> Self {
        Self { permission_service }
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::permission::handler;
        use axum::routing::{delete, get, post, put};

        // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
        let container_for_middleware = Arc::clone(container);

        Router::new()
            .route(
                "/permissions",
                post(handler::create_permission).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("permissions:create"),
                )),
            )
            .route(
                "/permissions",
                get(handler::get_permissions).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("permissions:read"),
                )),
            )
            .route(
                "/permissions/{id}",
                get(handler::get_permission).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("permissions:read"),
                )),
            )
            .route(
                "/permissions/{id}",
                put(handler::update_permission).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("permissions:update"),
                )),
            )
            .route(
                "/permissions/{id}",
                delete(handler::delete_permission).route_layer(middleware::from_fn_with_state(
                    container_for_middleware,
                    require_permission("permissions:delete"),
                )),
            )
            .with_state(self.permission_service)
    }
}

/// Role Router (SOLID: Single Responsibility)
struct RoleRouter {
    role_service: std::sync::Arc<dyn crate::modules::role::service_trait::RoleServiceTrait>,
}

impl RoleRouter {
    fn new(
        role_service: std::sync::Arc<dyn crate::modules::role::service_trait::RoleServiceTrait>,
    ) -> Self {
        Self { role_service }
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::role::handler;
        use axum::routing::{delete, get, post, put};

        // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
        let container_for_middleware = Arc::clone(container);

        Router::new()
            .route(
                "/roles",
                post(handler::create_role).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("roles:create"),
                )),
            )
            .route(
                "/roles",
                get(handler::get_roles).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("roles:read"),
                )),
            )
            .route(
                "/roles/{id}",
                get(handler::get_role).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("roles:read"),
                )),
            )
            .route(
                "/roles/{id}",
                put(handler::update_role).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("roles:update"),
                )),
            )
            .route(
                "/roles/{id}",
                delete(handler::delete_role).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("roles:delete"),
                )),
            )
            .route(
                "/roles/{id}/permissions",
                get(handler::get_role_permissions).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("roles:read"),
                )),
            )
            .route(
                "/roles/{id}/permissions",
                put(handler::update_role_permissions).route_layer(middleware::from_fn_with_state(
                    container_for_middleware,
                    require_permission("roles:update"),
                )),
            )
            .with_state(self.role_service)
    }
}

/// Profile Router (SOLID: Single Responsibility)
struct ProfileRouter;

impl ProfileRouter {
    fn new(_auth_service: Arc<dyn AuthServiceTrait>) -> Self {
        ProfileRouter
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::user::profile_handler;
        use axum::routing::get;

        Router::new()
            .route("/profile/me", get(profile_handler::get_profile))
            .layer(middleware::from_fn(
                crate::routes::middleware::api_type_middleware,
            ))
            .with_state(Arc::clone(container))
    }
}

/// Progression Router (SOLID: Single Responsibility)
struct ProgressionRouter {
    exp_system_service: crate::modules::progression::exp_system::DynExpSystemService,
}

impl ProgressionRouter {
    fn new(
        exp_system_service: crate::modules::progression::exp_system::DynExpSystemService,
    ) -> Self {
        Self { exp_system_service }
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::progression::exp_system::handler;
        use axum::routing::{get, post};

        // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
        let container_for_middleware = Arc::clone(container);

        // Routes with shared handlers (support both public and private)
        let shared_routes = Router::new()
            .route("/leaderboard", get(handler::get_leaderboard))
            .route("/users/{id}/level", get(handler::get_user_level_shared))
            .layer(middleware::from_fn_with_state(
                Arc::clone(container),
                crate::routes::middleware::optional_auth_middleware,
            ))
            .layer(middleware::from_fn(
                crate::routes::middleware::api_type_middleware,
            ))
            .with_state(self.exp_system_service.clone());

        // Routes with authentication required
        let private_routes = Router::new()
            .route(
                "/users/{id}/experience",
                post(handler::add_experience).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("progression:update"),
                )),
            )
            .with_state(self.exp_system_service);

        // Merge both routers
        shared_routes.merge(private_routes)
    }
}

/// Category Router (SOLID: Single Responsibility)
struct CategoryRouter {
    category_service: Arc<dyn CategoryServiceTrait>,
}

impl CategoryRouter {
    fn new(category_service: Arc<dyn CategoryServiceTrait>) -> Self {
        Self { category_service }
    }

    /// Create public routes (no authentication required)
    fn create_public_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::category::handler;
        use axum::routing::get;

        // Routes with shared handlers (support both public and private)
        Router::new()
            .route("/categories", get(handler::get_categories_shared))
            .route("/categories/{id}", get(handler::get_category_by_id_shared))
            .layer(middleware::from_fn_with_state(
                Arc::clone(container),
                crate::routes::middleware::optional_auth_middleware,
            ))
            .layer(middleware::from_fn(
                crate::routes::middleware::api_type_middleware,
            ))
            .with_state(self.category_service)
    }

    /// Create private routes (authentication required)
    fn create_private_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::category::handler;
        use axum::routing::{delete, get, patch, post, put};

        // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
        let container_for_middleware = Arc::clone(container);

        // Routes with authentication required
        Router::new()
            .route(
                "/categories",
                post(handler::create_category).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("categories:create"),
                )),
            )
            .route(
                "/categories/{id}",
                put(handler::update_category).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("categories:update"),
                )),
            )
            .route(
                "/categories/{id}",
                delete(handler::delete_category).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("categories:delete"),
                )),
            )
            .route(
                "/categories/{id}/toggle-status",
                patch(handler::toggle_category_status).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("categories:update"),
                )),
            )
            .route(
                "/categories/type/{category_type}",
                get(handler::get_categories_by_type).route_layer(middleware::from_fn_with_state(
                    container_for_middleware,
                    require_permission("categories:read"),
                )),
            )
            .with_state(self.category_service)
    }
}

/// Simple health check
async fn health_check() -> &'static str {
    "OK"
}

/// Detailed health check with database status
async fn detailed_health_check(
    State(container): State<Arc<ServiceContainer>>,
) -> axum::response::Json<serde_json::Value> {
    use serde_json::json;

    // Check database connectivity
    let db_healthy = container.database().is_healthy().await;
    let pool_status = container.database().pool_status();

    let health_status = json!({
        "status": if db_healthy { "healthy" } else { "unhealthy" },
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "services": {
            "database": {
                "status": if db_healthy { "up" } else { "down" },
                "connections": {
                    "active": pool_status,
                    "idle": 0
                }
            }
        }
    });

    axum::response::Json(health_status)
}

/// Laptop Router (SOLID: Single Responsibility)
struct LaptopRouter {
    laptop_service: Arc<dyn LaptopManagementServiceTrait>,
}

impl LaptopRouter {
    fn new(laptop_service: Arc<dyn LaptopManagementServiceTrait>) -> Self {
        Self { laptop_service }
    }

    /// Create public routes (no authentication required)
    fn create_public_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::laptop::handler;
        use axum::routing::{get, post};

        Router::new()
            .route("/laptops", get(handler::get_laptops))
            .route("/laptops/by-slug/{slug}", get(handler::get_laptop_by_slug))
            .layer(middleware::from_fn_with_state(
                Arc::clone(container),
                crate::routes::middleware::optional_auth_middleware,
            ))
            .layer(middleware::from_fn(
                crate::routes::middleware::api_type_middleware,
            ))
            .route(
                "/laptops/{id}/view",
                post(handler::increment_laptop_view_count),
            )
            .route(
                "/laptops/{laptop_id}/prices",
                get(handler::get_prices_by_laptop_id),
            )
            .route(
                "/laptops/{laptop_id}/specifications",
                get(handler::get_specifications_by_laptop_id),
            )
            .with_state(self.laptop_service)
    }

    /// Create private routes (authentication required)
    fn create_private_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::laptop::handler;
        use axum::routing::{delete, get, post, put};

        // OPTIMIZATION: Use Arc::clone() instead of .clone() for better performance
        let container_for_middleware = Arc::clone(container);

        Router::new()
            // Laptop management routes
            .route(
                "/laptops/complete",
                post(handler::create_complete_laptop).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:create"),
                )),
            )
            .route(
                "/laptops/{id}",
                get(handler::get_laptop_by_id).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:read"),
                )),
            )
            .route(
                "/laptops/{id}",
                put(handler::update_laptop).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:update"),
                )),
            )
            .route(
                "/laptops/{id}",
                delete(handler::delete_laptop).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:delete"),
                )),
            )
            // Status management routes
            .route(
                "/laptops/{id}/publish",
                put(handler::publish_laptop).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:publish"),
                )),
            )
            .route(
                "/laptops/{id}/archive",
                put(handler::archive_laptop).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:archive"),
                )),
            )
            .route(
                "/laptops/{id}/featured",
                put(handler::set_laptop_featured).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("laptops:feature"),
                )),
            )
            // Specification routes
            .route(
                "/specifications",
                post(handler::create_specification).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("specs:create"),
                )),
            )
            .route(
                "/specifications/{id}",
                get(handler::get_specification_by_id).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("specs:read"),
                )),
            )
            .route(
                "/specifications/{id}",
                put(handler::update_specification).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("specs:update"),
                )),
            )
            .route(
                "/specifications/{id}",
                delete(handler::delete_specification).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("specs:delete"),
                )),
            )
            // Price routes
            .route(
                "/prices",
                post(handler::create_price).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("prices:create"),
                )),
            )
            .route(
                "/prices/{id}",
                get(handler::get_price_by_id).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("prices:read"),
                )),
            )
            .route(
                "/prices/{id}",
                put(handler::update_price).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("prices:update"),
                )),
            )
            .route(
                "/prices/{id}",
                delete(handler::delete_price).route_layer(middleware::from_fn_with_state(
                    container_for_middleware.clone(),
                    require_permission("prices:delete"),
                )),
            )
            .route(
                "/laptops/{laptop_id}/prices/{price_id}/set-current",
                put(handler::set_current_price).route_layer(middleware::from_fn_with_state(
                    container_for_middleware,
                    require_permission("prices:update"),
                )),
            )
            .with_state(self.laptop_service)
    }
}

/// Redis Router (SOLID: Single Responsibility)
struct RedisRouter;

impl RedisRouter {
    fn new() -> Self {
        Self
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::redis::handler;
        use axum::routing::{delete, get, post};

        Router::new()
            .route("/redis/status", get(handler::redis_status))
            .route("/redis/ping", get(handler::ping_redis))
            .route("/redis/keys", get(handler::list_keys))
            .route("/redis/queue/email_events", get(handler::check_email_queue))
            .route("/redis/{key}", get(handler::get_key))
            .route("/redis/{key}", post(handler::set_key))
            .route("/redis/{key}", delete(handler::delete_key))
            .with_state(container.as_ref().clone())
    }
}

/// Image Router (SOLID: Single Responsibility)
struct ImageRouter;

impl ImageRouter {
    fn new() -> Self {
        Self
    }

    fn create_routes(self, container: &Arc<ServiceContainer>) -> Router {
        use crate::modules::image::handler;
        use axum::routing::{delete, get, post};

        Router::new()
            .route("/image/signature", post(handler::generate_image_signature))
            .route("/image/validate", post(handler::validate_image_url))
            .route("/image/delete", delete(handler::delete_image))
            .route("/image/extract-public-id", post(handler::extract_public_id))
            .route("/image/health", get(handler::cloudinary_health_check))
            .with_state(container.image_service())
    }
}
