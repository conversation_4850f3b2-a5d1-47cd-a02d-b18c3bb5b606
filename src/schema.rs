// SQLx schema definitions
// This file contains SQL queries and type definitions for the database schema

use serde::{Deserialize, Serialize};

// ===== ENUM TYPES =====

#[derive(
    Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, utoipa::ToSchema, sqlx::Type,
)]
#[sqlx(type_name = "screen_resolution_enum")]
pub enum ScreenResolution {
    #[serde(rename = "FHD")]
    #[sqlx(rename = "FHD")]
    FHD,
    #[serde(rename = "2K")]
    #[sqlx(rename = "2K")]
    TwoK,
    #[serde(rename = "2.5K")]
    #[sqlx(rename = "2.5K")]
    TwoPointFiveK,
    #[serde(rename = "3K")]
    #[sqlx(rename = "3K")]
    ThreeK,
    #[serde(rename = "3.5K")]
    #[sqlx(rename = "3.5K")]
    ThreePointFiveK,
    #[serde(rename = "4K")]
    #[sqlx(rename = "4K")]
    FourK,
}

impl std::fmt::Display for ScreenResolution {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ScreenResolution::FHD => write!(f, "FHD"),
            ScreenResolution::TwoK => write!(f, "2K"),
            ScreenResolution::TwoPointFiveK => write!(f, "2.5K"),
            ScreenResolution::ThreeK => write!(f, "3K"),
            ScreenResolution::ThreePointFiveK => write!(f, "3.5K"),
            ScreenResolution::FourK => write!(f, "4K"),
        }
    }
}

impl std::str::FromStr for ScreenResolution {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "FHD" => Ok(ScreenResolution::FHD),
            "2K" => Ok(ScreenResolution::TwoK),
            "2.5K" => Ok(ScreenResolution::TwoPointFiveK),
            "3K" => Ok(ScreenResolution::ThreeK),
            "3.5K" => Ok(ScreenResolution::ThreePointFiveK),
            "4K" => Ok(ScreenResolution::FourK),
            _ => Err(format!("Invalid screen resolution: {s}")),
        }
    }
}

#[derive(
    Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, utoipa::ToSchema, sqlx::Type,
)]
#[sqlx(type_name = "laptop_status_enum")]
pub enum LaptopStatus {
    #[serde(rename = "draft")]
    #[sqlx(rename = "draft")]
    Draft,
    #[serde(rename = "published")]
    #[sqlx(rename = "published")]
    Published,
    #[serde(rename = "archived")]
    #[sqlx(rename = "archived")]
    Archived,
}

impl std::fmt::Display for LaptopStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            LaptopStatus::Draft => write!(f, "draft"),
            LaptopStatus::Published => write!(f, "published"),
            LaptopStatus::Archived => write!(f, "archived"),
        }
    }
}

impl std::str::FromStr for LaptopStatus {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "draft" => Ok(LaptopStatus::Draft),
            "published" => Ok(LaptopStatus::Published),
            "archived" => Ok(LaptopStatus::Archived),
            _ => Err(format!("Invalid laptop status: {s}")),
        }
    }
}

#[derive(
    Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, utoipa::ToSchema, sqlx::Type,
)]
#[sqlx(type_name = "ram_type_enum")]
pub enum RamType {
    #[serde(rename = "DDR3")]
    #[sqlx(rename = "DDR3")]
    DDR3,
    #[serde(rename = "DDR4")]
    #[sqlx(rename = "DDR4")]
    DDR4,
    #[serde(rename = "DDR5")]
    #[sqlx(rename = "DDR5")]
    DDR5,
    #[serde(rename = "LPDDR4")]
    #[sqlx(rename = "LPDDR4")]
    LPDDR4,
    #[serde(rename = "LPDDR5")]
    #[sqlx(rename = "LPDDR5")]
    LPDDR5,
}

impl std::fmt::Display for RamType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RamType::DDR3 => write!(f, "DDR3"),
            RamType::DDR4 => write!(f, "DDR4"),
            RamType::DDR5 => write!(f, "DDR5"),
            RamType::LPDDR4 => write!(f, "LPDDR4"),
            RamType::LPDDR5 => write!(f, "LPDDR5"),
        }
    }
}

impl std::str::FromStr for RamType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "DDR3" => Ok(RamType::DDR3),
            "DDR4" => Ok(RamType::DDR4),
            "DDR5" => Ok(RamType::DDR5),
            "LPDDR4" => Ok(RamType::LPDDR4),
            "LPDDR5" => Ok(RamType::LPDDR5),
            _ => Err(format!("Invalid RAM type: {s}")),
        }
    }
}

#[derive(
    Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, utoipa::ToSchema, sqlx::Type,
)]
#[sqlx(type_name = "market_region_enum")]
pub enum MarketRegion {
    #[serde(rename = "Global")]
    #[sqlx(rename = "Global")]
    Global,
    #[serde(rename = "US")]
    #[sqlx(rename = "US")]
    US,
    #[serde(rename = "EU")]
    #[sqlx(rename = "EU")]
    EU,
    #[serde(rename = "Asia")]
    #[sqlx(rename = "Asia")]
    Asia,
    #[serde(rename = "Vietnam")]
    #[sqlx(rename = "Vietnam")]
    Vietnam,
}

impl std::fmt::Display for MarketRegion {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MarketRegion::Global => write!(f, "Global"),
            MarketRegion::US => write!(f, "US"),
            MarketRegion::EU => write!(f, "EU"),
            MarketRegion::Asia => write!(f, "Asia"),
            MarketRegion::Vietnam => write!(f, "Vietnam"),
        }
    }
}

impl std::str::FromStr for MarketRegion {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Global" => Ok(MarketRegion::Global),
            "US" => Ok(MarketRegion::US),
            "EU" => Ok(MarketRegion::EU),
            "Asia" => Ok(MarketRegion::Asia),
            "Vietnam" => Ok(MarketRegion::Vietnam),
            _ => Err(format!("Invalid market region: {s}")),
        }
    }
}

#[derive(
    Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, utoipa::ToSchema, sqlx::Type,
)]
#[sqlx(type_name = "currency_enum")]
pub enum Currency {
    #[serde(rename = "USD")]
    #[sqlx(rename = "USD")]
    USD,
    #[serde(rename = "EUR")]
    #[sqlx(rename = "EUR")]
    EUR,
    #[serde(rename = "VND")]
    #[sqlx(rename = "VND")]
    VND,
}

impl std::fmt::Display for Currency {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Currency::USD => write!(f, "USD"),
            Currency::EUR => write!(f, "EUR"),
            Currency::VND => write!(f, "VND"),
        }
    }
}

impl std::str::FromStr for Currency {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "USD" => Ok(Currency::USD),
            "EUR" => Ok(Currency::EUR),
            "VND" => Ok(Currency::VND),
            _ => Err(format!("Invalid currency: {s}")),
        }
    }
}

#[derive(
    Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, utoipa::ToSchema, sqlx::Type,
)]
#[sqlx(type_name = "price_region_enum")]
pub enum PriceRegion {
    #[serde(rename = "Global")]
    #[sqlx(rename = "Global")]
    Global,
    #[serde(rename = "US")]
    #[sqlx(rename = "US")]
    US,
    #[serde(rename = "EU")]
    #[sqlx(rename = "EU")]
    EU,
    #[serde(rename = "Asia")]
    #[sqlx(rename = "Asia")]
    Asia,
    #[serde(rename = "Vietnam")]
    #[sqlx(rename = "Vietnam")]
    Vietnam,
}

impl std::fmt::Display for PriceRegion {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PriceRegion::Global => write!(f, "Global"),
            PriceRegion::US => write!(f, "US"),
            PriceRegion::EU => write!(f, "EU"),
            PriceRegion::Asia => write!(f, "Asia"),
            PriceRegion::Vietnam => write!(f, "Vietnam"),
        }
    }
}

impl std::str::FromStr for PriceRegion {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Global" => Ok(PriceRegion::Global),
            "US" => Ok(PriceRegion::US),
            "EU" => Ok(PriceRegion::EU),
            "Asia" => Ok(PriceRegion::Asia),
            "Vietnam" => Ok(PriceRegion::Vietnam),
            _ => Err(format!("Invalid price region: {s}")),
        }
    }
}

// ===== SQL QUERIES =====

pub mod queries {

    // Users table queries
    pub const CREATE_USER: &str = r#"
        INSERT INTO users (id, username, fullname, password_hash, email, oauth_provider, oauth_provider_id, oauth_avatar_url, email_verified, created_at, updated_at, permission_version)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING *
    "#;

    pub const FIND_USER_BY_ID: &str = "SELECT * FROM users WHERE id = $1";
    pub const FIND_USER_BY_EMAIL: &str = "SELECT * FROM users WHERE email = $1";
    pub const FIND_USER_BY_USERNAME: &str = "SELECT * FROM users WHERE username = $1";
    pub const FIND_ALL_USERS: &str = "SELECT * FROM users ORDER BY created_at DESC";
    pub const UPDATE_USER: &str = r#"
        UPDATE users 
        SET email = COALESCE($2, email), 
            username = COALESCE($3, username), 
            fullname = COALESCE($4, fullname), 
            password_hash = COALESCE($5, password_hash),
            updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_USER: &str = "DELETE FROM users WHERE id = $1";
    pub const EXISTS_USER_BY_EMAIL: &str = "SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)";
    pub const EXISTS_USER_BY_USERNAME: &str =
        "SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)";

    // Categories table queries
    pub const CREATE_CATEGORY: &str = r#"
        INSERT INTO categories (id, name, slug, description, category_type, is_active, created_by, updated_by, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
    "#;
    pub const FIND_CATEGORY_BY_ID: &str = "SELECT * FROM categories WHERE id = $1";
    pub const FIND_CATEGORY_BY_SLUG_AND_TYPE: &str =
        "SELECT * FROM categories WHERE slug = $1 AND category_type = $2";
    pub const UPDATE_CATEGORY: &str = r#"
        UPDATE categories 
        SET name = $2, slug = $3, description = $4, category_type = $5, is_active = $6, updated_by = $7, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_CATEGORY: &str = "DELETE FROM categories WHERE id = $1";
    pub const FIND_ALL_CATEGORIES: &str = "SELECT * FROM categories ORDER BY name ASC";
    pub const FIND_CATEGORIES_BY_TYPE: &str =
        "SELECT * FROM categories WHERE category_type = $1 ORDER BY name ASC";
    pub const EXISTS_CATEGORY_BY_NAME_AND_TYPE: &str = "SELECT EXISTS(SELECT 1 FROM categories WHERE name = $1 AND category_type = $2 AND id != $3)";
    pub const EXISTS_CATEGORY_BY_SLUG_AND_TYPE: &str = "SELECT EXISTS(SELECT 1 FROM categories WHERE slug = $1 AND category_type = $2 AND id != $3)";

    // Laptops table queries
    pub const CREATE_LAPTOP: &str = r#"
        INSERT INTO laptops (id, category_id, brand, model, full_name, slug, sku, market_region, release_date, description, image_urls, status, is_featured, view_count, created_by, updated_by, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
        RETURNING *
    "#;
    pub const FIND_LAPTOP_BY_ID: &str = "SELECT * FROM laptops WHERE id = $1";
    pub const FIND_LAPTOP_BY_SLUG: &str = "SELECT * FROM laptops WHERE slug = $1";
    pub const UPDATE_LAPTOP: &str = r#"
        UPDATE laptops 
        SET category_id = $2, brand = $3, model = $4, full_name = $5, slug = $6, sku = $7, market_region = $8, release_date = $9, description = $10, image_urls = $11, status = $12, is_featured = $13, updated_by = $14, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_LAPTOP: &str = "DELETE FROM laptops WHERE id = $1";
    pub const UPDATE_LAPTOP_STATUS: &str =
        "UPDATE laptops SET status = $2, updated_by = $3, updated_at = NOW() WHERE id = $1";
    pub const UPDATE_LAPTOP_FEATURED: &str =
        "UPDATE laptops SET is_featured = $2, updated_by = $3, updated_at = NOW() WHERE id = $1";
    pub const INCREMENT_LAPTOP_VIEW_COUNT: &str =
        "UPDATE laptops SET view_count = view_count + 1 WHERE id = $1";
    pub const EXISTS_LAPTOP_BY_SLUG: &str =
        "SELECT EXISTS(SELECT 1 FROM laptops WHERE slug = $1 AND id != $2)";
    pub const EXISTS_LAPTOP_BY_SKU: &str =
        "SELECT EXISTS(SELECT 1 FROM laptops WHERE sku = $1 AND id != $2)";

    // Specifications table queries
    pub const CREATE_SPECIFICATION: &str = r#"
        INSERT INTO specifications (id, laptop_id, cpu_brand, cpu_model, ram_size, ram_type, ram_slots_total, ram_slots_available, ram_max_capacity, ram_soldered, storage_type, storage_capacity, storage_slots_total, storage_slots_available, storage_max_capacity, gpu_type, gpu_model, screen_size, screen_resolution, refresh_rate, weight, operating_system, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24)
        RETURNING *
    "#;
    pub const FIND_SPECIFICATION_BY_ID: &str = "SELECT * FROM specifications WHERE id = $1";
    pub const FIND_SPECIFICATION_BY_LAPTOP_ID: &str =
        "SELECT * FROM specifications WHERE laptop_id = $1";
    pub const UPDATE_SPECIFICATION: &str = r#"
        UPDATE specifications 
        SET laptop_id = $2, cpu_brand = $3, cpu_model = $4, ram_size = $5, ram_type = $6, ram_slots_total = $7, ram_slots_available = $8, ram_max_capacity = $9, ram_soldered = $10, storage_type = $11, storage_capacity = $12, storage_slots_total = $13, storage_slots_available = $14, storage_max_capacity = $15, gpu_type = $16, gpu_model = $17, screen_size = $18, screen_resolution = $19, refresh_rate = $20, weight = $21, operating_system = $22, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_SPECIFICATION: &str = "DELETE FROM specifications WHERE id = $1";
    pub const DELETE_SPECIFICATION_BY_LAPTOP_ID: &str =
        "DELETE FROM specifications WHERE laptop_id = $1";

    // Prices table queries
    pub const CREATE_PRICE: &str = r#"
        INSERT INTO prices (id, laptop_id, min_price, max_price, currency, source, region, effective_date, is_current, created_by, updated_by, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING *
    "#;
    pub const FIND_PRICE_BY_ID: &str = "SELECT * FROM prices WHERE id = $1";
    pub const FIND_PRICES_BY_LAPTOP_ID: &str =
        "SELECT * FROM prices WHERE laptop_id = $1 ORDER BY effective_date DESC";
    pub const UPDATE_PRICE: &str = r#"
        UPDATE prices 
        SET laptop_id = $2, min_price = $3, max_price = $4, currency = $5, source = $6, region = $7, effective_date = $8, is_current = $9, updated_by = $10, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_PRICE: &str = "DELETE FROM prices WHERE id = $1";
    pub const DELETE_PRICES_BY_LAPTOP_ID: &str = "DELETE FROM prices WHERE laptop_id = $1";
    pub const FIND_CURRENT_PRICES_BY_LAPTOP_ID: &str = "SELECT * FROM prices WHERE laptop_id = $1 AND is_current = true ORDER BY effective_date DESC";
    pub const SET_PRICES_NOT_CURRENT_BY_LAPTOP_ID: &str =
        "UPDATE prices SET is_current = false WHERE laptop_id = $1";
    pub const SET_PRICE_CURRENT: &str =
        "UPDATE prices SET is_current = true WHERE id = $1 AND laptop_id = $2";

    // Permissions table queries
    pub const CREATE_PERMISSION: &str = r#"
        INSERT INTO permissions (id, name, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
    "#;
    pub const FIND_PERMISSION_BY_ID: &str = "SELECT * FROM permissions WHERE id = $1";
    pub const FIND_PERMISSION_BY_NAME: &str = "SELECT * FROM permissions WHERE name = $1";
    pub const FIND_PERMISSIONS_BY_IDS: &str = "SELECT * FROM permissions WHERE id = ANY($1)";
    pub const FIND_PERMISSIONS_BY_NAMES: &str = "SELECT * FROM permissions WHERE name = ANY($1)";
    pub const FIND_ALL_PERMISSIONS: &str = "SELECT * FROM permissions ORDER BY name ASC";
    pub const UPDATE_PERMISSION: &str = r#"
        UPDATE permissions 
        SET name = $2, description = $3, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_PERMISSION: &str = "DELETE FROM permissions WHERE id = $1";
    pub const EXISTS_PERMISSION_BY_NAME: &str =
        "SELECT EXISTS(SELECT 1 FROM permissions WHERE name = $1)";
    pub const EXISTS_PERMISSION_BY_NAME_EXCLUDE_ID: &str =
        "SELECT EXISTS(SELECT 1 FROM permissions WHERE name = $1 AND id != $2)";

    // Roles table queries
    pub const CREATE_ROLE: &str = r#"
        INSERT INTO roles (id, name, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
    "#;
    pub const FIND_ROLE_BY_ID: &str = "SELECT * FROM roles WHERE id = $1";
    pub const FIND_ROLE_BY_NAME: &str = "SELECT * FROM roles WHERE name = $1";
    pub const FIND_ROLES_BY_IDS: &str = "SELECT * FROM roles WHERE id = ANY($1)";
    pub const FIND_ROLES_BY_NAMES: &str = "SELECT * FROM roles WHERE name = ANY($1)";
    pub const FIND_ALL_ROLES: &str = "SELECT * FROM roles ORDER BY name ASC";
    pub const UPDATE_ROLE: &str = r#"
        UPDATE roles 
        SET name = $2, description = $3, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_ROLE: &str = "DELETE FROM roles WHERE id = $1";
    pub const EXISTS_ROLE_BY_NAME: &str = "SELECT EXISTS(SELECT 1 FROM roles WHERE name = $1)";
    pub const EXISTS_ROLE_BY_NAME_EXCLUDE_ID: &str =
        "SELECT EXISTS(SELECT 1 FROM roles WHERE name = $1 AND id != $2)";

    // User-Role junction table queries
    pub const ASSIGN_ROLE_TO_USER: &str = "INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2) ON CONFLICT (user_id, role_id) DO NOTHING";
    pub const REMOVE_ROLE_FROM_USER: &str =
        "DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2";
    pub const REMOVE_ALL_ROLES_FOR_USER: &str = "DELETE FROM user_roles WHERE user_id = $1";
    pub const GET_USER_ROLES: &str = "SELECT role_id FROM user_roles WHERE user_id = $1";
    pub const GET_ROLE_NAMES_FOR_USER: &str = r#"
        SELECT r.name FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1
    "#;
    pub const USER_HAS_ROLE: &str =
        "SELECT EXISTS(SELECT 1 FROM user_roles WHERE user_id = $1 AND role_id = $2)";
    pub const GET_ROLES_FOR_USERS: &str = r#"
        SELECT ur.user_id, r.name as role_name FROM user_roles ur
        INNER JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = ANY($1)
    "#;
    pub const REPLACE_ROLES_FOR_USER_DELETE: &str = "DELETE FROM user_roles WHERE user_id = $1";
    pub const REPLACE_ROLES_FOR_USER_INSERT: &str =
        "INSERT INTO user_roles (user_id, role_id) SELECT $1, unnest($2::uuid[])";

    // Role-Permission junction table queries
    pub const ASSIGN_PERMISSION_TO_ROLE: &str = "INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2) ON CONFLICT (role_id, permission_id) DO NOTHING";
    pub const REMOVE_PERMISSION_FROM_ROLE: &str =
        "DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2";
    pub const GET_ROLE_PERMISSIONS: &str =
        "SELECT permission_id FROM role_permissions WHERE role_id = $1";
    pub const ROLE_HAS_PERMISSION: &str =
        "SELECT EXISTS(SELECT 1 FROM role_permissions WHERE role_id = $1 AND permission_id = $2)";
    pub const REPLACE_PERMISSIONS_FOR_ROLE_DELETE: &str =
        "DELETE FROM role_permissions WHERE role_id = $1";
    pub const REPLACE_PERMISSIONS_FOR_ROLE_INSERT: &str =
        "INSERT INTO role_permissions (role_id, permission_id) SELECT $1, unnest($2::uuid[])";
    pub const LIST_PERMISSIONS_FOR_ROLE: &str = r#"
        SELECT p.* FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role_id = $1
        ORDER BY p.name
    "#;
    pub const COUNT_USERS_FOR_ROLE: &str = "SELECT COUNT(*) FROM user_roles WHERE role_id = $1";

    // User levels and experience queries
    pub const CREATE_USER_LEVEL: &str = r#"
        INSERT INTO user_levels (id, user_id, level, experience, total_experience, current_title_id, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
    "#;
    pub const GET_USER_LEVEL: &str = "SELECT * FROM user_levels WHERE user_id = $1";
    pub const UPDATE_USER_LEVEL: &str = r#"
        UPDATE user_levels 
        SET level = $2, experience = $3, total_experience = $4, current_title_id = $5, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const ADD_EXPERIENCE_HISTORY: &str = r#"
        INSERT INTO user_experience_history (id, user_id, experience_gained, source, metadata, created_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
    "#;
    pub const GET_EXPERIENCE_HISTORY: &str =
        "SELECT * FROM user_experience_history WHERE user_id = $1 ORDER BY created_at DESC";
    pub const SET_CURRENT_TITLE: &str = r#"
        UPDATE user_levels 
        SET current_title_id = $2, updated_at = NOW()
        WHERE user_id = $1
        RETURNING *
    "#;

    // Titles queries
    pub const CREATE_TITLE: &str = r#"
        INSERT INTO titles (id, name, description, level_required, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
    "#;
    pub const FIND_TITLE_BY_ID: &str = "SELECT * FROM titles WHERE id = $1";
    pub const FIND_TITLE_BY_NAME: &str = "SELECT * FROM titles WHERE name = $1";
    pub const FIND_ALL_TITLES: &str = "SELECT * FROM titles ORDER BY level_required ASC";
    pub const UPDATE_TITLE: &str = r#"
        UPDATE titles 
        SET name = $2, description = $3, level_required = $4, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_TITLE: &str = "DELETE FROM titles WHERE id = $1";
    pub const EXISTS_TITLE_BY_NAME: &str = "SELECT EXISTS(SELECT 1 FROM titles WHERE name = $1)";
    pub const EXISTS_TITLE_BY_NAME_EXCLUDE_ID: &str =
        "SELECT EXISTS(SELECT 1 FROM titles WHERE name = $1 AND id != $2)";

    // User unlocked titles queries
    pub const UNLOCK_TITLE_FOR_USER: &str = "INSERT INTO user_unlocked_titles (user_id, title_id, unlocked_at) VALUES ($1, $2, $3) ON CONFLICT (user_id, title_id) DO NOTHING";
    pub const GET_USER_UNLOCKED_TITLES: &str =
        "SELECT title_id FROM user_unlocked_titles WHERE user_id = $1";
    pub const USER_HAS_TITLE: &str =
        "SELECT EXISTS(SELECT 1 FROM user_unlocked_titles WHERE user_id = $1 AND title_id = $2)";

    // Laptop embeddings queries
    pub const CREATE_LAPTOP_EMBEDDING: &str = r#"
        INSERT INTO laptop_embeddings (id, laptop_id, embedding, model_version, embedding_type, status, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
    "#;
    pub const FIND_LAPTOP_EMBEDDING_BY_ID: &str = "SELECT * FROM laptop_embeddings WHERE id = $1";
    pub const FIND_LAPTOP_EMBEDDINGS_BY_LAPTOP_ID: &str =
        "SELECT * FROM laptop_embeddings WHERE laptop_id = $1";
    pub const UPDATE_LAPTOP_EMBEDDING: &str = r#"
        UPDATE laptop_embeddings 
        SET embedding = $2, model_version = $3, embedding_type = $4, status = $5, updated_at = NOW()
        WHERE id = $1
        RETURNING *
    "#;
    pub const DELETE_LAPTOP_EMBEDDING: &str = "DELETE FROM laptop_embeddings WHERE id = $1";
    pub const DELETE_LAPTOP_EMBEDDINGS_BY_LAPTOP_ID: &str =
        "DELETE FROM laptop_embeddings WHERE laptop_id = $1";
}
